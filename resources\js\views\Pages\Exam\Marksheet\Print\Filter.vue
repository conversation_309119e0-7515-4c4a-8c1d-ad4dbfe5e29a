<template>
    <FilterForm
        :init-form="initForm"
        :multiple="['students']"
        :form="form"
        @hide="emit('hide')"
    >
        <div class="grid grid-cols-3 gap-6" v-if="fetchData.isLoaded">
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.type"
                    name="type"
                    :label="$trans('exam.marksheet.type')"
                    :options="preRequisites.types"
                    v-model:error="formErrors.type"
                    @change="onTypeChange"
                />
            </div>
            <div
                class="col-span-3 sm:col-span-1"
                v-if="state.selectedType?.requiresTerm"
            >
                <BaseSelect
                    v-model="form.term"
                    name="term"
                    :label="$trans('exam.term.term')"
                    :options="preRequisites.terms"
                    value-prop="uuid"
                    v-model:error="formErrors.term"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.name }}
                        <span class="ml-1"
                            >({{
                                slotProps.value.division?.name ||
                                $trans("general.all")
                            }})</span
                        >
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.name }}
                        <span class="ml-1"
                            >({{
                                slotProps.option.division?.name ||
                                $trans("general.all")
                            }})</span
                        >
                    </template>
                </BaseSelect>
            </div>
            <div
                class="col-span-3 sm:col-span-1"
                v-if="state.selectedType?.requiresExam"
            >
                <BaseSelect
                    v-model="form.exam"
                    name="exam"
                    :label="$trans('exam.exam')"
                    value-prop="uuid"
                    :options="preRequisites.exams"
                    v-model:error="formErrors.exam"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.name }}
                        <span class="ml-1" v-if="slotProps.value.term"
                            >({{
                                slotProps.value.term?.division?.name ||
                                $trans("general.all")
                            }})</span
                        >
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.name }}
                        <span class="ml-1" v-if="slotProps.option.term"
                            >({{
                                slotProps.option.term?.division?.name ||
                                $trans("general.all")
                            }})</span
                        >
                    </template>
                </BaseSelect>
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-if="fetchData.isLoaded"
                    v-model="form.attempt"
                    name="attempt"
                    :label="$trans('exam.schedule.props.attempt')"
                    :options="preRequisites.attempts"
                    v-model:error="formErrors.attempt"
                />
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelectSearch
                    v-if="fetchData.isLoaded"
                    name="batch"
                    :label="
                        $trans('global.select', {
                            attribute: $trans('academic.batch.batch'),
                        })
                    "
                    v-model="form.batch"
                    v-model:error="formErrors.batch"
                    value-prop="uuid"
                    :init-search="fetchData.batch"
                    search-key="course_batch"
                    search-action="academic/batch/list"
                    @selected="onBatchSelect"
                    @removed="onBatchRemove"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.course.name }}
                        {{ slotProps.value.name }}
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.course.nameWithTerm }}
                        {{ slotProps.option.name }}
                    </template>
                </BaseSelectSearch>
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-if="form.batch"
                    multiple
                    name="students"
                    :label="
                        $trans('global.select', {
                            attribute: $trans('student.student'),
                        })
                    "
                    :options="state.students"
                    v-model="form.students"
                    v-model:error="formErrors.students"
                    track-by="name"
                    value-prop="uuid"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.name }} ({{
                            slotProps.value.courseName +
                            " " +
                            slotProps.value.batchName
                        }})
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.name }} ({{
                            slotProps.option.courseName +
                            " " +
                            slotProps.option.batchName
                        }})
                    </template>
                </BaseSelect>
            </div>
        </div>

        <BaseFieldset
            class="mt-4"
            v-if="
                fetchData.isLoaded && !actingAs(['student', 'guardian'], 'any')
            "
        >
            <template #legend>
                <div class="flex items-center gap-2">
                    {{
                        $trans("global.show", {
                            attribute: $trans("general.options"),
                        })
                    }}
                    <BaseSwitch
                        reverse
                        v-model="showOptions"
                        name="showOptions"
                    />
                </div>
            </template>
            <template v-if="showOptions">
                <div class="grid grid-cols-3 gap-6">
                    <div class="col-span-3 sm:col-span-1">
                        <BaseSwitch
                            vertical
                            v-model="form.showCourseWise"
                            name="showCourseWise"
                            :label="$trans('exam.report.course_wise')"
                            v-model:error="formErrors.showCourseWise"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <BaseSwitch
                            vertical
                            v-model="form.showAllStudent"
                            name="showAllStudent"
                            :label="
                                $trans('global.list_all', {
                                    attribute: $trans('student.student'),
                                })
                            "
                            v-model:error="formErrors.showAllStudent"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <BaseInput
                            type="number"
                            v-model="form.column"
                            name="column"
                            :label="$trans('print.column')"
                            v-model:error="formErrors.column"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <BaseInput
                            :leading-text="$trans('list.unit.mm')"
                            type="number"
                            v-model="form.marginTop"
                            name="marginTop"
                            :label="$trans('print.margin_top')"
                            v-model:error="formErrors.marginTop"
                        />
                    </div>
                </div>
            </template>
        </BaseFieldset>
    </FilterForm>
</template>

<script setup>
import { reactive, ref, onMounted, watch } from "vue"
import { useRoute } from "vue-router"
import { useStore } from "vuex"
import { toBoolean } from "@core/helpers/string"
import { actingAs, getFormErrors } from "@core/helpers/action"

const route = useRoute()
const store = useStore()

const emit = defineEmits(["hide"])

const props = defineProps({
    initUrl: {
        type: String,
        default: "",
    },
    preRequisites: {
        type: Object,
        default() {
            return {}
        },
    },
    marksheetFilters: {
        type: Object,
        default: null,
    },
})

const initForm = {
    type: "",
    term: "",
    exam: "",
    attempt: "first",
    batch: "",
    students: [],
    showCourseWise: false,
    showAllStudent: false,
    column: 1,
    marginTop: 0,
}

const isLoading = ref(false)
const showOptions = ref(false)
const formErrors = getFormErrors(props.initUrl)

// Use form from composable if provided, otherwise use local form
const form = props.marksheetFilters ? props.marksheetFilters.form : reactive({ ...initForm })

const state = reactive({
    selectedType: "",
    terms: props.preRequisites.terms,
    exams: props.preRequisites.exams,
    students: [],
})

const fetchData = reactive({
    term: "",
    exam: "",
    batch: "",
    students: [],
    isLoaded:
        route.query.term ||
        route.query.exam ||
        route.query.batch ||
        route.query.students
            ? false
            : true,
})

const cancel = () => {
    emit("cancel")
}

const onTypeChange = (type) => {
    state.selectedType = props.preRequisites.types.find((t) => t.value === type)
}

const onBatchSelect = async (batch) => {
    state.students = []
    form.students = []

    let status = "studying"

    if (form.showAllStudent) {
        status = "all"
    }

    isLoading.value = true
    await store
        .dispatch("student/listAll", {
            params: {
                batch: batch.uuid,
                status: status,
            },
        })
        .then((response) => {
            state.students = response
            isLoading.value = false
        })
        .catch((e) => {
            isLoading.value = false
        })

    fetchData.students = []
}

const onBatchRemove = () => {
    state.students = []
    form.students = []
    fetchData.students = []
}

onMounted(async () => {
    if (_.isEmpty(route.query)) {
        fetchData.isLoaded = true
        return
    }

    fetchData.term = route.query.term
    form.term = route.query.term
    fetchData.exam = route.query.exam
    form.exam = route.query.exam
    fetchData.batch = route.query.batch
    form.batch = route.query.batch
    form.showCourseWise = toBoolean(route.query.showCourseWise || "")
    form.showAllStudent = toBoolean(route.query.showAllStudent || "")

    if (route.query.batch) {
        await onBatchSelect({ uuid: route.query.batch })
        form.students = route.query.students
            ? route.query.students.split(",")
            : []
    }

    fetchData.isLoaded = true
})

watch(
    props.preRequisites,
    (newVal) => {
        if (newVal) {
            state.selectedType = newVal.types.find(
                (t) => t.value === route.query.type
            )
        }
    },
    { deep: true }
)
</script>
