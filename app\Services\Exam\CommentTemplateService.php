<?php

namespace App\Services\Exam;

use App\Http\Resources\Academic\CourseResource;
use App\Models\Academic\Course;
use App\Models\Exam\CommentTemplate;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class CommentTemplateService
{
    public function preRequisite(Request $request)
    {
        $courses = CourseResource::collection(Course::query()
            ->with('division')
            ->byPeriod()
            ->get());

        $types = [
            ['label' => trans('exam.comment_template.types.academic'), 'value' => 'academic'],
            ['label' => trans('exam.comment_template.types.behavioral'), 'value' => 'behavioral'],
        ];

        return compact('courses', 'types');
    }

    public function create(Request $request): CommentTemplate
    {
        \DB::beginTransaction();

        $commentTemplate = CommentTemplate::forceCreate($this->formatParams($request));

        \DB::commit();

        return $commentTemplate;
    }

    public function update(Request $request, CommentTemplate $commentTemplate): void
    {
        \DB::beginTransaction();

        $commentTemplate->forceFill($this->formatParams($request))->save();

        \DB::commit();
    }

    public function deletable(CommentTemplate $commentTemplate): void
    {
        // Add any deletion validation logic here if needed
    }

    private function formatParams(Request $request, ?CommentTemplate $commentTemplate = null): array
    {
        $formatted = [
            'name' => $request->name,
            'content' => $request->content,
            'type' => $request->type,
            'is_active' => $request->boolean('is_active', true),
            'description' => $request->description,
        ];

        if ($request->type === 'academic' && $request->has('course_rules')) {
            $formatted['course_rules'] = $this->formatCourseRules($request->course_rules);
        } else {
            $formatted['course_rules'] = null;
        }

        return $formatted;
    }

    private function formatCourseRules(array $courseRules): array
    {
        $formatted = [];

        if (isset($courseRules['progression_rules'])) {
            $formatted['progression_rules'] = [];
            
            foreach ($courseRules['progression_rules'] as $rule) {
                $formattedRule = [
                    'template' => $rule['template'] ?? '',
                ];

                if (isset($rule['course_name_pattern'])) {
                    $formattedRule['course_name_pattern'] = $rule['course_name_pattern'];
                }

                if (isset($rule['course_uuid'])) {
                    $formattedRule['course_uuid'] = $rule['course_uuid'];
                }

                $formatted['progression_rules'][] = $formattedRule;
            }
        }

        return $formatted;
    }

    /**
     * Get templates for comment interface
     */
    public function getTemplatesForComment(Request $request): array
    {
        $academicTemplates = CommentTemplate::active()
            ->academic()
            ->orderBy('name')
            ->get();

        $behavioralTemplates = CommentTemplate::active()
            ->behavioral()
            ->orderBy('name')
            ->get();

        return [
            'academic' => $academicTemplates,
            'behavioral' => $behavioralTemplates,
        ];
    }

    /**
     * Process template for specific course and return processed content
     */
    public function processTemplate(CommentTemplate $template, Course $course): string
    {
        $content = $template->processForCourse($course);
        
        // Additional processing for next course name placeholder
        if (strpos($content, '{next_course_name}') !== false) {
            $nextCourseName = $template->getNextCourseName($course);
            $content = str_replace('{next_course_name}', $nextCourseName ?? 'next level', $content);
        }

        return $content;
    }

    /**
     * Apply template to multiple students
     */
    public function applyTemplateToStudents(CommentTemplate $template, Course $course, array $studentUuids, string $commentType = 'comment'): array
    {
        $processedContent = $this->processTemplate($template, $course);
        
        $comments = [];
        foreach ($studentUuids as $studentUuid) {
            $comments[] = [
                'uuid' => $studentUuid,
                'result' => $commentType === 'result' ? $processedContent : '',
                'comment' => $commentType === 'comment' ? $processedContent : '',
            ];
        }

        return $comments;
    }
}
