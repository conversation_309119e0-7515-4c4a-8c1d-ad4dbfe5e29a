<?php

use App\Http\Controllers\Exam\AdmitCardController;
use App\Http\Controllers\Exam\AssessmentController;
use App\Http\Controllers\Exam\BulkDownloadController;
use App\Http\Controllers\Exam\AttendanceController;
use App\Http\Controllers\Exam\CommentController;
use App\Http\Controllers\Exam\CommentTemplateController;
use App\Http\Controllers\Exam\ExamActionController;
use App\Http\Controllers\Exam\ExamController;
use App\Http\Controllers\Exam\FormActionController;
use App\Http\Controllers\Exam\FormController;
use App\Http\Controllers\Exam\GradeController;
use App\Http\Controllers\Exam\MarkController;
use App\Http\Controllers\Exam\MarkImportController;
use App\Http\Controllers\Exam\MarksheetController;
use App\Http\Controllers\Exam\MarksheetPrintController;
use App\Http\Controllers\Exam\MarksheetProcessController;
use App\Http\Controllers\Exam\ObservationController;
use App\Http\Controllers\Exam\ObservationMarkController;
use App\Http\Controllers\Exam\OnlineExamActionController;
use App\Http\Controllers\Exam\OnlineExamController;
use App\Http\Controllers\Exam\OnlineExamQuestionActionController;
use App\Http\Controllers\Exam\OnlineExamQuestionController;
use App\Http\Controllers\Exam\OnlineExamSubmissionController;
use App\Http\Controllers\Exam\OnlineExamProctorController;
use App\Http\Controllers\Exam\QuestionBankActionController;
use App\Http\Controllers\Exam\QuestionBankController;
use App\Http\Controllers\Exam\QuestionBankExportController;
use App\Http\Controllers\Exam\QuestionBankImportController;
use App\Http\Controllers\Exam\OnlineExamSubmitController;
use App\Http\Controllers\Exam\Report\CumulativeMarkSummaryController;
use App\Http\Controllers\Exam\Report\ExamSummaryController;
use App\Http\Controllers\Exam\Report\MarkSummaryController;
use App\Http\Controllers\Exam\ScheduleActionController;
use App\Http\Controllers\Exam\ScheduleController;
use App\Http\Controllers\Exam\TermController;
use Illuminate\Support\Facades\Route;

// Exam Routes
Route::name('exam.')->prefix('exam')->group(function () {
    Route::get('grades/pre-requisite', [GradeController::class, 'preRequisite'])->name('grades.preRequisite')->middleware('permission:exam-grade:manage');
    Route::apiResource('grades', GradeController::class)->middleware('permission:exam-grade:manage');

    Route::get('assessments/pre-requisite', [AssessmentController::class, 'preRequisite'])->name('assessments.preRequisite')->middleware('permission:exam-assessment:manage');
    Route::apiResource('assessments', AssessmentController::class)->middleware('permission:exam-assessment:manage');

    Route::get('observations/pre-requisite', [ObservationController::class, 'preRequisite'])->name('observations.preRequisite')->middleware('permission:exam-observation:manage');
    Route::apiResource('observations', ObservationController::class)->middleware('permission:exam-observation:manage');

    Route::get('terms/pre-requisite', [TermController::class, 'preRequisite'])->name('terms.preRequisite')->middleware('permission:exam-term:manage');
    Route::apiResource('terms', TermController::class)->middleware('permission:exam-term:manage');

    Route::get('schedules/pre-requisite', [ScheduleController::class, 'preRequisite'])->name('schedules.preRequisite');

    Route::patch('schedules/{schedule}/toggle-publish-admit-card', [ScheduleActionController::class, 'togglePublishAdmitCard'])->name('exams.togglePublishAdmitCard');

    Route::patch('schedules/{schedule}/form', [ScheduleActionController::class, 'updateForm'])->name('schedules.updateForm');

    Route::post('schedules/{schedule}/form/confirm', [ScheduleActionController::class, 'confirmForm'])->name('schedules.confirmForm');
    Route::post('schedules/{schedule}/form', [ScheduleActionController::class, 'submitForm'])->name('schedules.submitForm');

    Route::post('schedules/{schedule}/copy', [ScheduleActionController::class, 'copyToCourse'])->name('schedules.copyToCourse');

    Route::apiResource('schedules', ScheduleController::class);

    Route::get('online-exams/pre-requisite', [OnlineExamController::class, 'preRequisite'])->name('online-exams.preRequisite');

    Route::get('online-exams/pre-requisite-for-filter', [OnlineExamController::class, 'preRequisiteForFilter'])->name('online-exams.preRequisiteForFilter');

    Route::get('online-exams/{onlineExam}/questions/pre-requisite', [OnlineExamQuestionController::class, 'preRequisite'])->name('online-exams.questions.preRequisite');

    Route::post('online-exams/{onlineExam}/questions/reorder', [OnlineExamQuestionActionController::class, 'reorder'])->name('online-exams.questions.reorder');

    Route::post('online-exams/{onlineExam}/questions/import-from-question-bank', [OnlineExamQuestionController::class, 'importFromQuestionBank'])->name('online-exams.questions.importFromQuestionBank');

    Route::apiResource('online-exams.questions', OnlineExamQuestionController::class);

    Route::get('online-exams/{onlineExam}/submissions/{submission}/questions', [OnlineExamSubmissionController::class, 'getQuestions'])->name('online-exams.submissions.getQuestions');

    Route::post('online-exams/{onlineExam}/submissions/{submission}/evaluate', [OnlineExamSubmissionController::class, 'evaluate'])->name('online-exams.submissions.evaluate');

    Route::apiResource('online-exams.submissions', OnlineExamSubmissionController::class)->only(['index', 'destroy']);

    Route::get('online-exams/{onlineExam}/live-questions', [OnlineExamSubmitController::class, 'getQuestions'])->name('online-exams.submit.getQuestions');
    Route::post('online-exams/{onlineExam}/start', [OnlineExamSubmitController::class, 'startSubmission'])->name('online-exams.submit.start');
    Route::post('online-exams/{onlineExam}/submit', [OnlineExamSubmitController::class, 'submit'])->name('online-exams.submit');
    Route::post('online-exams/{onlineExam}/finish-submit', [OnlineExamSubmitController::class, 'finishSubmission'])->name('online-exams.submit.finish');
    Route::get('online-exams/{onlineExam}/time-status', [OnlineExamSubmitController::class, 'getTimeStatus'])->name('online-exams.submit.timeStatus');
    Route::post('online-exams/{onlineExam}/status', [OnlineExamActionController::class, 'updateStatus'])->name('online-exams.updateStatus');
    Route::get('online-exams/{onlineExam}/preview-questions', [OnlineExamController::class, 'getPreviewQuestions'])->name('online-exams.previewQuestions');

    Route::apiResource('online-exams', OnlineExamController::class);

    Route::get('question-banks/pre-requisite', [QuestionBankController::class, 'preRequisite'])->name('question-banks.preRequisite');

    Route::get('question-banks/pre-requisite-for-filter', [QuestionBankController::class, 'preRequisiteForFilter'])->name('question-banks.preRequisiteForFilter');

    Route::post('question-banks/import', QuestionBankImportController::class)->middleware('permission:question-bank:create')->name('question-banks.import');

    Route::post('question-banks/parse-questions', [QuestionBankController::class, 'parseQuestions'])->middleware('permission:question-bank:create')->name('question-banks.parseQuestions');

    Route::post('question-banks/bulk-create', [QuestionBankController::class, 'bulkCreate'])->middleware('permission:question-bank:create')->name('question-banks.bulkCreate');

    Route::post('question-banks/bulk-delete', [QuestionBankActionController::class, 'bulkDelete'])->middleware('permission:question-bank:delete')->name('question-banks.bulkDelete');

    Route::get('question-banks/export', QuestionBankExportController::class)->name('question-banks.export');

    Route::apiResource('question-banks', QuestionBankController::class);

    Route::get('forms/pre-requisite', [FormController::class, 'preRequisite'])->name('forms.preRequisite');

    Route::post('forms/{form}/status', [FormActionController::class, 'updateStatus'])->name('forms.updateStatus');

    Route::get('forms/{form}/print', [FormActionController::class, 'print'])->name('forms.print');

    Route::get('forms/{form}/print-admit-card', [FormActionController::class, 'printAdmitCard'])->name('forms.printAdmitCard');

    Route::apiResource('forms', FormController::class)->only(['index', 'show', 'destroy']);

    Route::get('mark/pre-requisite', [MarkController::class, 'preRequisite'])->name('mark.preRequisite');

    Route::post('mark/import', MarkImportController::class)->middleware('permission:exam:marks-record')->name('mark.import');

    Route::get('mark/fetch', [MarkController::class, 'fetch'])->name('mark.fetch');
    Route::post('mark', [MarkController::class, 'store'])->name('mark.store');
    Route::delete('mark', [MarkController::class, 'remove'])->name('mark.remove');

    Route::get('observation-mark/pre-requisite', [ObservationMarkController::class, 'preRequisite'])->name('observation-mark.preRequisite');
    Route::get('observation-mark/fetch', [ObservationMarkController::class, 'fetch'])->name('observation-mark.fetch');
    Route::post('observation-mark', [ObservationMarkController::class, 'store'])->name('observation-mark.store');
    Route::delete('observation-mark', [ObservationMarkController::class, 'remove'])->name('observation-mark.remove');

    Route::get('comment/pre-requisite', [CommentController::class, 'preRequisite'])->name('comment.preRequisite');
    Route::get('comment/fetch', [CommentController::class, 'fetch'])->name('comment.fetch');
    Route::post('comment', [CommentController::class, 'store'])->name('comment.store');
    Route::post('comment/apply-template', [CommentController::class, 'applyTemplate'])->name('comment.applyTemplate');
    Route::delete('comment', [CommentController::class, 'remove'])->name('comment.remove');

    Route::get('comment-templates/pre-requisite', [CommentTemplateController::class, 'preRequisite'])->name('comment-templates.preRequisite')->middleware('permission:exam-comment-template:manage');
    Route::apiResource('comment-templates', CommentTemplateController::class)->middleware('permission:exam-comment-template:manage');

    Route::get('attendance/pre-requisite', [AttendanceController::class, 'preRequisite'])->name('attendance.preRequisite');
    Route::get('attendance/fetch', [AttendanceController::class, 'fetch'])->name('attendance.fetch');
    Route::post('attendance', [AttendanceController::class, 'store'])->name('attendance.store');
    Route::delete('attendance', [AttendanceController::class, 'remove'])->name('attendance.remove');

    Route::middleware('permission:exam-admit-card:access')->group(function () {
        Route::get('admit-card/pre-requisite', [AdmitCardController::class, 'preRequisite'])->name('admit-card.preRequisite');
        Route::get('admit-card', [AdmitCardController::class, 'fetchReport'])->name('admit-card.fetchReport');
    });

    Route::middleware('permission:exam-marksheet:access')->group(function () {
        Route::get('marksheet/pre-requisite', [MarksheetController::class, 'preRequisite'])->name('marksheet.preRequisite');
        Route::get('marksheet', [MarksheetController::class, 'fetchReport'])->name('marksheet.fetchReport');
        Route::get('marksheet/download-pdf', [MarksheetController::class, 'downloadPdf'])->name('marksheet.downloadPdf');
    });

    Route::middleware('permission:exam-marksheet:access')->group(function () {
        Route::get('marksheet/process/pre-requisite', [MarksheetProcessController::class, 'preRequisite'])->name('marksheet.process.preRequisite');
        Route::get('marksheet/process', [MarksheetProcessController::class, 'process'])->name('marksheet.process');
    });

    Route::middleware('permission:exam-marksheet:access')->group(function () {
        Route::get('marksheet/print/pre-requisite', [MarksheetPrintController::class, 'preRequisite'])->name('marksheet.print.preRequisite');
        Route::get('marksheet/print', [MarksheetPrintController::class, 'print'])->name('marksheet.print');
        Route::get('marksheet/print/download-pdf', [MarksheetPrintController::class, 'downloadPdf'])->name('marksheet.print.downloadPdf');
    });

    Route::prefix('reports')->name('reports.')->middleware('permission:exam:report')->group(function () {
        Route::get('mark-summary/pre-requisite', [MarkSummaryController::class, 'preRequisite'])->name('mark-summary.preRequisite');
        Route::get('mark-summary', [MarkSummaryController::class, 'fetchReport'])->name('mark-summary.fetchReport');
        Route::get('mark-summary/download-pdf', [MarkSummaryController::class, 'downloadPdf'])->name('mark-summary.downloadPdf');

        Route::get('cumulative-mark-summary/pre-requisite', [CumulativeMarkSummaryController::class, 'preRequisite'])->name('cumulative-mark-summary.preRequisite');
        Route::get('cumulative-mark-summary', [CumulativeMarkSummaryController::class, 'fetchReport'])->name('cumulative-mark-summary.fetchReport');
        Route::get('cumulative-mark-summary/download-pdf', [CumulativeMarkSummaryController::class, 'downloadPdf'])->name('cumulative-mark-summary.downloadPdf');

        Route::get('exam-summary/pre-requisite', [ExamSummaryController::class, 'preRequisite'])->name('exam-summary.preRequisite');
        Route::get('exam-summary', [ExamSummaryController::class, 'fetchReport'])->name('exam-summary.fetchReport');
        Route::get('exam-summary/download-pdf', [ExamSummaryController::class, 'downloadPdf'])->name('exam-summary.downloadPdf');
        Route::post('bulk-download', [BulkDownloadController::class, 'download'])->name('bulk-download');
        Route::post('bulk-download/estimate', [BulkDownloadController::class, 'estimate'])->name('bulk-download.estimate');
    });
});

Route::middleware('permission:exam:manage')->group(function () {
    Route::get('exams/pre-requisite', [ExamController::class, 'preRequisite'])->name('exams.preRequisite');

    Route::post('exams/{exam}/config', [ExamActionController::class, 'storeConfig'])->name('exams.storeConfig');

    Route::apiResource('exams', ExamController::class);
});

// Proctoring Routes
Route::prefix('online-exams/{onlineExam}/proctoring')->name('online-exams.proctoring.')->middleware('proctoring.security')->group(function () {
    // Student proctoring endpoints
    Route::post('validate-requirements', [OnlineExamProctorController::class, 'validateRequirements'])->name('validateRequirements');
    Route::post('webcam-capture', [OnlineExamProctorController::class, 'logWebcamCapture'])->name('logWebcamCapture');
    Route::post('audio-alert', [OnlineExamProctorController::class, 'logAudioAlert'])->name('logAudioAlert');
    Route::post('screen-recording', [OnlineExamProctorController::class, 'logScreenRecording'])->name('logScreenRecording');
    Route::post('tab-switch', [OnlineExamProctorController::class, 'logTabSwitch'])->name('logTabSwitch');
    Route::post('fullscreen-exit', [OnlineExamProctorController::class, 'logFullscreenExit'])->name('logFullscreenExit');
    Route::post('copy-paste-attempt', [OnlineExamProctorController::class, 'logCopyPasteAttempt'])->name('logCopyPasteAttempt');
    Route::post('face-detection-failure', [OnlineExamProctorController::class, 'logFaceDetectionFailure'])->name('logFaceDetectionFailure');

    // Examiner review endpoints
    Route::middleware('permission:online-exam:read')->group(function () {
        Route::get('submissions/{submission}/summary', [OnlineExamProctorController::class, 'getProctoringSummary'])->name('getProctoringSummary');
        Route::get('submissions/{submission}/logs', [OnlineExamProctorController::class, 'getProctorLogs'])->name('getProctorLogs');
        Route::get('logs/{log}/media', [OnlineExamProctorController::class, 'downloadMedia'])->name('downloadMedia');
    });
});
