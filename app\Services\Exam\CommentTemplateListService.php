<?php

namespace App\Services\Exam;

use App\Contracts\ListGenerator;
use App\Http\Resources\Exam\CommentTemplateResource;
use App\Models\Exam\CommentTemplate;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CommentTemplateListService extends ListGenerator
{
    protected $allowedSorts = ['created_at', 'updated_at', 'name', 'type', 'is_active'];

    protected $defaultSort = 'created_at';

    protected $defaultOrder = 'desc';

    public function getHeaders(): array
    {
        $headers = [
            [
                'key' => 'name',
                'label' => trans('exam.comment_template.props.name'),
                'sortable' => true,
                'visibility' => true,
            ],
            [
                'key' => 'type',
                'label' => trans('exam.comment_template.props.type'),
                'sortable' => true,
                'visibility' => true,
            ],
            [
                'key' => 'content',
                'label' => trans('exam.comment_template.props.content'),
                'sortable' => false,
                'visibility' => true,
            ],
            [
                'key' => 'isActive',
                'label' => trans('exam.comment_template.props.is_active'),
                'print_label' => 'is_active',
                'sortable' => true,
                'visibility' => true,
            ],
            [
                'key' => 'createdAt',
                'label' => trans('general.created_at'),
                'print_label' => 'created_at.formatted',
                'sortable' => true,
                'visibility' => true,
            ],
        ];

        if (request()->ajax()) {
            $headers[] = $this->actionHeader;
        }

        return $headers;
    }

    public function filter(Request $request): Builder
    {
        return CommentTemplate::query()
            ->when($request->query('name'), function ($q, $name) {
                return $q->where('name', 'like', '%'.$name.'%');
            })
            ->when($request->query('type'), function ($q, $type) {
                return $q->where('type', $type);
            })
            ->when($request->query('is_active') !== null, function ($q) use ($request) {
                return $q->where('is_active', $request->boolean('is_active'));
            });
    }

    public function paginate(Request $request): AnonymousResourceCollection
    {
        return CommentTemplateResource::collection($this->filter($request)
            ->orderBy($this->getSort(), $this->getOrder())
            ->paginate((int) $this->getPageLength(), ['*'], 'current_page'))
            ->additional([
                'headers' => $this->getHeaders(),
                'meta' => [
                    'allowed_sorts' => $this->allowedSorts,
                    'default_sort' => $this->defaultSort,
                    'default_order' => $this->defaultOrder,
                ],
            ]);
    }

    public function list(Request $request): AnonymousResourceCollection
    {
        return $this->paginate($request);
    }
}
