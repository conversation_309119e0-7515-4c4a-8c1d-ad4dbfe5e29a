<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Exam Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the exam module.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Bulk Download Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for bulk PDF download functionality
    |
    */
    'bulk_download' => [
        // Maximum number of files that can be downloaded in a single bulk operation
        'max_files' => env('EXAM_BULK_DOWNLOAD_MAX_FILES', 50),
        
        // Maximum estimated size in MB for bulk downloads
        'max_size_mb' => env('EXAM_BULK_DOWNLOAD_MAX_SIZE_MB', 100),
        
        // Timeout for bulk download operations (in seconds)
        'timeout' => env('EXAM_BULK_DOWNLOAD_TIMEOUT', 300),
        
        // Temporary directory for bulk downloads (relative to storage/app)
        'temp_dir' => 'temp/bulk-downloads',
        
        // Cleanup temporary files after this many hours
        'cleanup_after_hours' => env('EXAM_BULK_DOWNLOAD_CLEANUP_HOURS', 24),
    ],

    /*
    |--------------------------------------------------------------------------
    | PDF Generation Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for PDF generation
    |
    */
    'pdf' => [
        // Default paper size for PDF generation
        'default_paper_size' => env('EXAM_PDF_DEFAULT_PAPER_SIZE', 'A4-L'),
        
        // Available paper sizes
        'paper_sizes' => [
            'A4-P' => 'A4 Portrait',
            'A4-L' => 'A4 Landscape',
            'A3-P' => 'A3 Portrait',
            'A3-L' => 'A3 Landscape',
            'Letter-P' => 'Letter Portrait',
            'Letter-L' => 'Letter Landscape',
            'Legal-P' => 'Legal Portrait',
            'Legal-L' => 'Legal Landscape',
        ],
        
        // PDF margins (in mm)
        'margins' => [
            'left' => 10,
            'right' => 10,
            'top' => 10,
            'bottom' => 10,
        ],
        
        // Enable/disable PDF watermark
        'watermark' => [
            'enabled' => env('EXAM_PDF_WATERMARK_ENABLED', false),
            'text' => env('EXAM_PDF_WATERMARK_TEXT', 'CONFIDENTIAL'),
            'opacity' => env('EXAM_PDF_WATERMARK_OPACITY', 0.1),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for emailing results
    |
    */
    'email' => [
        // Enable/disable email functionality
        'enabled' => env('EXAM_EMAIL_ENABLED', true),
        
        // Maximum number of emails that can be sent in a single batch
        'max_batch_size' => env('EXAM_EMAIL_MAX_BATCH_SIZE', 20),
        
        // Delay between email batches (in seconds)
        'batch_delay' => env('EXAM_EMAIL_BATCH_DELAY', 5),
        
        // Email templates
        'templates' => [
            'result_notification' => 'emails.exam.result-notification',
            'bulk_result_notification' => 'emails.exam.bulk-result-notification',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for caching exam results
    |
    */
    'cache' => [
        // Enable/disable result caching
        'enabled' => env('EXAM_CACHE_ENABLED', true),
        
        // Cache TTL in minutes
        'ttl' => env('EXAM_CACHE_TTL', 60),
        
        // Cache key prefix
        'prefix' => 'exam_results',
    ],
];
