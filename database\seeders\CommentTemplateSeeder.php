<?php

namespace Database\Seeders;

use App\Models\Exam\CommentTemplate;
use Illuminate\Database\Seeder;

class CommentTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Academic Templates
        CommentTemplate::create([
            'name' => 'Promotion Template',
            'content' => 'Promoted to {next_course_name}',
            'type' => 'academic',
            'description' => 'Generic promotion comment for all courses',
            'is_active' => true,
            'course_rules' => [
                'progression_rules' => [
                    [
                        'course_name_pattern' => 'year',
                        'template' => 'Congratulations! You have been promoted to {next_course_name}. Keep up the excellent work!',
                    ],
                    [
                        'course_name_pattern' => 'grade',
                        'template' => 'Successfully completed Grade requirements. Promoted to {next_course_name}.',
                    ],
                ],
            ],
        ]);

        CommentTemplate::create([
            'name' => 'Academic Excellence',
            'content' => 'Excellent academic performance in {course_name}. Well done!',
            'type' => 'academic',
            'description' => 'For students with excellent academic performance',
            'is_active' => true,
        ]);

        // Behavioral Templates
        CommentTemplate::create([
            'name' => 'Excellent Behavior',
            'content' => 'Demonstrates excellent behavior and discipline throughout the academic year.',
            'type' => 'behavioral',
            'description' => 'For students with excellent behavior',
            'is_active' => true,
        ]);

        CommentTemplate::create([
            'name' => 'Good Participation',
            'content' => 'Shows good participation in class activities and maintains positive relationships with peers.',
            'type' => 'behavioral',
            'description' => 'For students with good class participation',
            'is_active' => true,
        ]);

        CommentTemplate::create([
            'name' => 'Needs Improvement',
            'content' => 'Needs to improve attention in class and complete assignments on time.',
            'type' => 'behavioral',
            'description' => 'For students who need behavioral improvement',
            'is_active' => true,
        ]);
    }
}
