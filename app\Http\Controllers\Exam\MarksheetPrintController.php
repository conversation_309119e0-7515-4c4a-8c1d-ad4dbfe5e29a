<?php

namespace App\Http\Controllers\Exam;

use App\Http\Controllers\Controller;
use App\Services\Exam\MarksheetPrintService;
use Illuminate\Http\Request;

class MarksheetPrintController extends Controller
{
    public function __construct() {}

    public function preRequisite(Request $request, MarksheetPrintService $service)
    {
        return response()->ok($service->preRequisite($request));
    }

    public function print(Request $request, MarksheetPrintService $service)
    {
        return $service->print($request);
    }

    public function downloadPdf(Request $request, MarksheetPrintService $service)
    {
        // Set action to pdf to trigger PDF generation
        $request->merge(['action' => 'pdf']);

        // Make sure paper_size is properly passed
        if ($request->has('paper_size')) {
            $request->merge(['paper_size' => $request->paper_size]);
        }

        return $service->print($request);
    }
}
