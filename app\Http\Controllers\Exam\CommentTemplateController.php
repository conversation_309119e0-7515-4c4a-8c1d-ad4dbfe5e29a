<?php

namespace App\Http\Controllers\Exam;

use App\Http\Controllers\Controller;
use App\Http\Requests\Exam\CommentTemplateRequest;
use App\Http\Resources\Exam\CommentTemplateResource;
use App\Models\Exam\CommentTemplate;
use App\Services\Exam\CommentTemplateListService;
use App\Services\Exam\CommentTemplateService;
use Illuminate\Http\Request;

class CommentTemplateController extends Controller
{
    public function __construct()
    {
        $this->middleware('test.mode.restriction')->only(['destroy']);
    }

    public function preRequisite(Request $request, CommentTemplateService $service)
    {
        return response()->ok($service->preRequisite($request));
    }

    public function index(Request $request, CommentTemplateListService $service)
    {
        return $service->paginate($request);
    }

    public function store(CommentTemplateRequest $request, CommentTemplateService $service)
    {
        $commentTemplate = $service->create($request);

        return response()->success([
            'message' => trans('global.created', ['attribute' => trans('exam.comment_template.comment_template')]),
            'comment_template' => CommentTemplateResource::make($commentTemplate),
        ]);
    }

    public function show(CommentTemplate $commentTemplate, CommentTemplateService $service)
    {
        return CommentTemplateResource::make($commentTemplate);
    }

    public function update(CommentTemplateRequest $request, CommentTemplate $commentTemplate, CommentTemplateService $service)
    {
        $service->update($request, $commentTemplate);

        return response()->success([
            'message' => trans('global.updated', ['attribute' => trans('exam.comment_template.comment_template')]),
        ]);
    }

    public function destroy(CommentTemplate $commentTemplate, CommentTemplateService $service)
    {
        $service->deletable($commentTemplate);

        $commentTemplate->delete();

        return response()->success([
            'message' => trans('global.deleted', ['attribute' => trans('exam.comment_template.comment_template')]),
        ]);
    }
}
