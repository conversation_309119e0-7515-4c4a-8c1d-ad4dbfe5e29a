import { mutations, actions, getters } from "@stores/global"

const initialState = () => ({
    initURL: "/app/exam/comment-templates",
    formErrors: {},
})

const commentTemplate = {
    namespaced: true,
    state: initialState,
    modules: {},
    mutations: {
        ...mutations,
    },
    actions: {
        ...actions,
    },
    getters: {
        ...getters,
    },
}

export default commentTemplate
