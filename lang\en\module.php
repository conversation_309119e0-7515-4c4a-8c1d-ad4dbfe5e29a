<?php

return [
    'general' => 'General',
    'user' => 'User',
    'todo' => 'Todo',
    'config' => 'Config',
    'role' => 'Role',
    'permission' => 'Permission',
    'locale' => 'Locale',
    'media' => 'Media',
    'option' => 'Option',
    'utility' => 'Utility',
    'custom_field' => 'Custom Field',
    'template' => 'Template',
    'modules' => 'Modules',
    'team' => 'Team',
    'dashboard' => 'Dashboard',
    'academic' => 'Academic',
    'department' => 'Department',
    'program' => 'Program',
    'session' => 'Session',
    'period' => 'Academic Session',
    'division' => 'Division',
    'course' => 'Class',
    'batch' => 'Class Arm',
    'subject' => 'Subject',
    'division_incharge' => 'Head of Division',
    'course_incharge' => 'Class Teacher',
    'batch_incharge' => 'Class Arm Teacher',
    'subject_incharge' => 'Subject Teacher',
    'certificate' => 'Certificate',
    'certificate_template' => 'Certificate Template',
    'id_card' => 'ID Card',
    'id_card_template' => 'ID Card Template',
    'class_timing' => 'Class Timing',
    'timetable' => 'Timetable',
    'fee_group' => 'Fee Group',
    'fee_head' => 'Fee Head',
    'fee_concession' => 'Fee Concession',
    'fee_structure' => 'Fee Structure',
    'fee_installment' => 'Fee Installment',
    'fee_allocation' => 'Fee Allocation',
    'fee_refund' => 'Fee Refund',
    'ledger_type' => 'Ledger Type',
    'ledger' => 'Ledger',
    'transaction' => 'Transaction',
    'transport_stoppage' => 'Transport Stoppage',
    'transport_route' => 'Transport Route',
    'transport_circle' => 'Transport Circle',
    'transport_fee' => 'Transport Fee',
    'vehicle' => 'Vehicle',
    'vehicle_travel_record' => 'Vehicle Travel Record',
    'vehicle_fuel_record' => 'Vehicle Fuel Record',
    'vehicle_service_record' => 'Vehicle Service Record',
    'contact' => 'Contact',
    'guardian' => 'Guardian',
    'student' => 'Student',
    'registration' => 'Registration',
    'admission' => 'Admission',
    'transfer_request' => 'Transfer Request',
    'diary' => 'Student Diary',
    'employee' => 'Employee',
    //'department' => 'Department',
    'designation' => 'Designation',
    'reception' => 'Reception',
    'enquiry' => 'Enquiry',
    'visitor_log' => 'Visitor Log',
    'gate_pass' => 'Gate Pass',
    'complaint' => 'Complaint',
    'query' => 'Query',
    'call_log' => 'Call Log',
    'correspondence' => 'Correspondence',
    'library' => 'Library',
    'book' => 'Book',
    'book_addition' => 'Book Addition',
    'book_transaction' => 'Book Transaction',
    'book_issue' => 'Issue Book',
    'book_return' => 'Return Book',
    'inventory' => 'Inventory',
    'mess' => 'Mess',
    'menu_item' => 'Menu Item',
    'meal_log' => 'Meal Log',
    'meal' => 'Meal',
    'stock_category' => 'Stock Category',
    'stock_item' => 'Stock Item',
    'stock_requisition' => 'Stock Requisition',
    'stock_purchase' => 'Stock Purchase',
    'stock_adjustment' => 'Stock Adjustment',
    'stock_transfer' => 'Stock Transfer',
    'holiday' => 'Holiday',
    'event' => 'Event',
    'resource' => 'Resource',
    'online_class' => 'Online Class',
    'assignment' => 'Assignment',
    'lesson_plan' => 'Lesson Plan',
    'learning_material' => 'Learning Material',
    'download' => 'Download',
    'syllabus' => 'Syllabus',
    'communication' => 'Communication',
    'announcement' => 'Announcement',
    'asset' => 'Asset',
    'building' => 'Building',
    'block' => 'Block',
    'floor' => 'Floor',
    'room' => 'Room',
    'exam' => 'Exam',
    'exam_term' => 'Exam Term',
    'exam_grade' => 'Exam Grade',
    'exam_schedule' => 'Exam Schedule',
    'exam_assessment' => 'Exam Assessment',
    'exam_observation' => 'Exam Observation',
    'exam_record' => 'Exam Record',
    'exam_comment_template' => 'Comment Template',
    'online_exam' => 'Online Exam',
    'online_exam_question' => 'Online Exam Question',
    'online_exam_submission' => 'Online Exam Submission',
    'finance' => 'Finance',
    'calendar' => 'Calendar',
    'transport' => 'Transport',
    'hostel' => 'Hostel',
    'room_allocation' => 'Room Allocation',
    'recruitment' => 'Recruitment',
    'job_vacancy' => 'Job Vacancy',
    'job_application' => 'Job Application',
    'gallery' => 'Gallery',
    'form' => 'Form',
    'site_page' => 'Page',
    'site_menu' => 'Menu',
    'site_block' => 'Block',
    'site' => 'Site',
    'blog' => 'Blog',
    'activity' => 'Activity',
    'trip' => 'Trip',
    'trip_visitor' => 'Trip Visitor',
    'discipline' => 'Discipline',
    'incident' => 'Incident',
    'ai' => 'AI',
    //'custom_field' => 'Custom Field',
];
