<template>
    <ListItem :init-url="initUrl" @setItems="setItems">
        <template #header>
            <PageHeader
                :title="$trans('exam.comment_template.comment_template')"
                :navs="[{ label: $trans('exam.exam'), path: 'Exam' }]"
            >
                <PageHeaderAction
                    url="exam/comment-templates/"
                    name="ExamCommentTemplate"
                    :title="$trans('exam.comment_template.comment_template')"
                    :actions="userActions"
                    :dropdown-actions="dropdownActions"
                />
            </PageHeader>
        </template>

        <ParentTransition appear :visibility="true">
            <DataTable
                :header="commentTemplates.headers"
                :meta="commentTemplates.meta"
                module="exam.comment_template"
                @refresh="emitter.emit('listItems')"
            >
                <DataRow
                    v-for="commentTemplate in commentTemplates.data"
                    :key="commentTemplate.uuid"
                    @double-click="
                        router.push({
                            name: 'ExamCommentTemplateEdit',
                            params: { uuid: commentTemplate.uuid },
                        })
                    "
                >
                    <DataCell name="name">
                        <div class="flex flex-col">
                            <span class="font-medium">{{ commentTemplate.name }}</span>
                            <span class="text-sm text-gray-500" v-if="commentTemplate.description">
                                {{ commentTemplate.description }}
                            </span>
                        </div>
                    </DataCell>

                    <DataCell name="type">
                        <BaseBadge
                            :design="commentTemplate.type === 'academic' ? 'success' : 'info'"
                            size="sm"
                        >
                            {{ commentTemplate.typeLabel }}
                        </BaseBadge>
                    </DataCell>

                    <DataCell name="content">
                        <div class="max-w-xs truncate" :title="commentTemplate.content">
                            {{ commentTemplate.content }}
                        </div>
                    </DataCell>

                    <DataCell name="isActive">
                        <BaseBadge
                            :design="commentTemplate.isActive ? 'success' : 'danger'"
                        >
                            {{ commentTemplate.isActive ? $trans('general.yes') : $trans('general.no') }}
                        </BaseBadge>
                    </DataCell>

                    <DataCell name="createdAt">
                        {{ commentTemplate.createdAt.formatted }}
                    </DataCell>

                    <DataCell name="action">
                        <FloatingMenu>
                            <FloatingMenuItem
                                icon="fas fa-edit"
                                @click="
                                    router.push({
                                        name: 'ExamCommentTemplateEdit',
                                        params: { uuid: commentTemplate.uuid },
                                    })
                                "
                            >
                                {{ $trans('general.edit') }}
                            </FloatingMenuItem>
                            <FloatingMenuItem
                                icon="fas fa-trash"
                                @click="
                                    emitter.emit('deleteItem', {
                                        uuid: commentTemplate.uuid,
                                    })
                                "
                            >
                                {{ $trans('general.delete') }}
                            </FloatingMenuItem>
                        </FloatingMenu>
                    </DataCell>
                </DataRow>
            </DataTable>
        </ParentTransition>
    </ListItem>
</template>

<script>
export default {
    name: "ExamCommentTemplateList",
}
</script>

<script setup>
import { reactive, inject } from "vue"
import { useRouter } from "vue-router"
import { perform } from "@core/helpers/action"

const router = useRouter()

const emitter = inject("emitter")

let userActions = []
if (perform("exam-comment-template:manage")) {
    userActions.push("create")
}

let dropdownActions = []
if (perform("exam-comment-template:manage")) {
    dropdownActions = ["print", "pdf", "excel"]
}

const initUrl = "exam/commentTemplate/"

const commentTemplates = reactive({})

const setItems = (data) => {
    Object.assign(commentTemplates, data)
}
</script>
