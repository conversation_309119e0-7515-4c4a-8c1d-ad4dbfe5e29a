export default [
    {
        path: "comment-templates",
        name: "ExamCommentTemplate",
        redirect: { name: "ExamCommentTemplateList" },
        meta: {
            label: "exam.comment_template.comment_template",
            icon: "fas fa-right-long",
            hideChildren: true,
            permissions: ["exam-comment-template:manage"],
            keySearch: true,
        },
        component: {
            template: "<router-view></router-view>",
        },
        children: [
            {
                path: "",
                name: "ExamCommentTemplateList",
                meta: {
                    trans: "global.list",
                    label: "exam.comment_template.comment_templates",
                },
                component: () =>
                    import("@views/Pages/Exam/CommentTemplate/Index.vue"),
            },
    {
        path: "comment-templates/create",
        name: "ExamCommentTemplateCreate",
        meta: {
            isNotNav: true,
            type: "create",
            action: "create",
            trans: "global.add",
            label: "exam.comment_template.comment_template",
            permissions: ["exam-comment-template:manage"],
            breadcrumbs: [
                {
                    label: "exam.comment_template.comment_templates",
                    to: "ExamCommentTemplateIndex",
                },
            ],
        },
        component: () =>
            import("@views/Pages/Exam/CommentTemplate/Form.vue"),
    },
    {
        path: "comment-templates/:uuid/edit",
        name: "ExamCommentTemplateEdit",
        meta: {
            isNotNav: true,
            type: "edit",
            action: "update",
            trans: "global.edit",
            label: "exam.comment_template.comment_template",
            permissions: ["exam-comment-template:manage"],
            breadcrumbs: [
                {
                    label: "exam.comment_template.comment_templates",
                    to: "ExamCommentTemplateIndex",
                },
            ],
        },
        component: () =>
            import("@views/Pages/Exam/CommentTemplate/Form.vue"),
    },
        ],
    },
]
