<template>
    <PageHeader
        :title="$trans(route.meta.label)"
        :navs="[{ label: $trans('exam.exam'), path: 'Exam' }]"
    >
        <PageHeaderAction>
            <BaseButton
                design="white"
                @click="router.push({ name: 'ExamMark' })"
                >{{ $trans("exam.mark") }}</BaseButton
            >
            <BaseButton
                design="white"
                @click="router.push({ name: 'ExamObservationMark' })"
                >{{ $trans("exam.observation_mark") }}</BaseButton
            >
            <BaseButton
                design="white"
                @click="router.push({ name: 'ExamAttendance' })"
                >{{ $trans("student.attendance.attendance") }}</BaseButton
            >
        </PageHeaderAction>
    </PageHeader>

    <ParentTransition appear :visibility="true">
        <FilterForm
            @afterFilter="fetchStudent"
            @cancel="clearFilter"
            :init-url="initUrl"
            :pre-requisites="preRequisites"
        ></FilterForm>
    </ParentTransition>

    <BaseCard no-padding no-content-padding :is-loading="isLoading">
        <template #title>
            {{ $trans("exam.record") }}
        </template>

        <template #action>
            <BaseButton
                design="error"
                @click="removeMark"
                v-if="state.meta.commentRecorded"
            >
                {{
                    $trans("global.remove", {
                        attribute: $trans("exam.comment"),
                    })
                }}
            </BaseButton>
        </template>

        <div class="p-2">
            <BaseAlert
                size="xs"
                design="error"
                v-if="form.students.length == 0"
                >{{ $trans("general.errors.record_not_found") }}</BaseAlert
            >
        </div>

        <!-- Template Application Section -->
        <div v-if="form.students.length && hasCommentTemplates && perform('exam-comment-template:manage')" class="p-4 bg-gray-50 dark:bg-gray-800 border-b">
            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                {{ $trans('exam.comment_template.apply_templates') }}
            </h4>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <BaseSelect
                        v-model="templateForm.selectedTemplate"
                        name="selectedTemplate"
                        :label="$trans('exam.comment_template.select_template')"
                        :options="getAllTemplates()"
                        label-prop="name"
                        value-prop="uuid"
                        :placeholder="$trans('global.select', { attribute: $trans('exam.comment_template.comment_template') })"
                    />
                </div>

                <div>
                    <BaseSelect
                        v-model="templateForm.commentType"
                        name="commentType"
                        :label="$trans('exam.comment_template.comment_type')"
                        :options="[
                            { label: $trans('exam.comment'), value: 'comment' },
                            { label: $trans('exam.comment_behavioural'), value: 'result' }
                        ]"
                        label-prop="label"
                        value-prop="value"
                    />
                </div>

                <div class="flex items-end">
                    <BaseButton
                        design="primary"
                        @click="applyTemplateToSelected"
                        :disabled="!templateForm.selectedTemplate || selectedStudents.length === 0"
                        class="mr-2"
                    >
                        {{ $trans('exam.comment_template.apply_to_selected') }} ({{ selectedStudents.length }})
                    </BaseButton>

                    <BaseButton
                        design="secondary"
                        @click="applyTemplateToAll"
                        :disabled="!templateForm.selectedTemplate"
                    >
                        {{ $trans('exam.comment_template.apply_to_all') }}
                    </BaseButton>
                </div>
            </div>

            <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {{ $trans('exam.comment_template.template_help_text') }}
            </div>
        </div>

        <FormAction
            v-if="form.students.length"
            no-card
            button-padding
            :keep-adding="false"
            :stay-on="true"
            :init-url="initUrl"
            action="store"
            :init-form="initForm"
            :form="form"
        >
            <div class="divide-y divide-gray-200 dark:divide-gray-700">
                <div
                    class="grid grid-cols-4 gap-6 px-4 py-2"
                    v-for="(student, index) in form.students"
                    :key="student.uuid"
                >
                    <div class="col-span-4 sm:col-span-1">
                        <BaseDataView>
                            <div class="flex items-center space-x-2">
                                <BaseCheckbox
                                    v-model="student.selected"
                                    :name="`students.${index}.selected`"
                                    :label="''"
                                />
                                <span>{{ student.name }} ({{ student.rollNumber || student.codeNumber }})</span>
                            </div>
                            <div class="mt-1">
                                <BaseCheckbox
                                    v-model="student.isNotApplicable"
                                    :name="`students.${index}.isNotApplicable`"
                                    :label="
                                        $trans('global.is_not', {
                                            attribute: $trans(
                                                'exam.schedule.props.applicable'
                                            ),
                                        })
                                    "
                                ></BaseCheckbox>
                            </div>
                        </BaseDataView>
                    </div>
                    <div
                        class="col-span-4 sm:col-span-3"
                        v-if="!student.isNotApplicable"
                    >
                        <BaseInput
                            type="text"
                            v-model="student.result"
                            :name="`students.${index}.result`"
                            :placeholder="$trans('exam.comment_behavioural')"
                            v-model:error="
                                formErrors[`students.${index}.result`]
                            "
                        />

                        <BaseInput
                            type="text"
                            v-model="student.comment"
                            :name="`students.${index}.comment`"
                            :placeholder="$trans('exam.comment')"
                            v-model:error="
                                formErrors[`students.${index}.comment`]
                            "
                        />
                    </div>
                </div>
            </div>
        </FormAction>
    </BaseCard>
</template>

<script>
export default {
    name: "ExamComment",
}
</script>

<script setup>
import { ref, reactive, inject, onMounted, computed } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useStore } from "vuex"
import { cloneDeep } from "@core/utils"
import { getFormErrors, perform } from "@core/helpers/action"
import FilterForm from "./Filter.vue"
import { confirmAction } from "@core/helpers/alert"

const route = useRoute()
const router = useRouter()
const store = useStore()

const emitter = inject("emitter")

const initForm = {
    exam: "",
    batch: "",
    students: [],
}

const initUrl = "exam/comment/"
const isLoading = ref(false)

const preRequisites = reactive({
    exams: [],
    commentTemplates: {
        academic: [],
        behavioral: [],
    },
})
const formErrors = getFormErrors(initUrl)
const form = reactive({ ...initForm })
const state = reactive({
    meta: {},
    showComment: true,
})

const templateForm = reactive({
    selectedTemplate: "",
    commentType: "comment",
})

const selectedStudents = computed(() => {
    return form.students.filter(student => student.selected && !student.isNotApplicable)
})

const hasCommentTemplates = computed(() => {
    return preRequisites.commentTemplates &&
           (preRequisites.commentTemplates.academic?.length > 0 ||
            preRequisites.commentTemplates.behavioral?.length > 0)
})



const preRequisite = async () => {
    isLoading.value = true
    await store
        .dispatch(initUrl + "preRequisite")
        .then((response) => {
            isLoading.value = false
            Object.assign(preRequisites, response)
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const clearFilter = () => {
    initForm.exam = ""
    initForm.batch = ""
    initForm.students = []
    Object.assign(form, cloneDeep(initForm))
}

const fetchStudent = async () => {
    if (!route.query.batch) {
        return
    }

    isLoading.value = true
    await store
        .dispatch(initUrl + "fetch", {
            params: route.query,
        })
        .then((response) => {
            isLoading.value = false
            initForm.exam = route.query.exam
            initForm.batch = route.query.batch
            initForm.students = response.data.map(student => ({
                ...student,
                selected: false
            }))
            state.meta = response.meta
            Object.assign(form, cloneDeep(initForm))
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const getAllTemplates = () => {
    const academic = preRequisites.commentTemplates.academic || []
    const behavioral = preRequisites.commentTemplates.behavioral || []
    return [...academic, ...behavioral]
}

const applyTemplateToSelected = async () => {
    if (!templateForm.selectedTemplate || selectedStudents.value.length === 0) {
        return
    }

    const studentUuids = selectedStudents.value.map(student => student.uuid)
    await applyTemplate(studentUuids)
}

const applyTemplateToAll = async () => {
    if (!templateForm.selectedTemplate) {
        return
    }

    const studentUuids = form.students
        .filter(student => !student.isNotApplicable)
        .map(student => student.uuid)
    await applyTemplate(studentUuids)
}

const applyTemplate = async (studentUuids) => {
    isLoading.value = true

    const payload = {
        exam: route.query.exam,
        batch: route.query.batch,
        template_uuid: templateForm.selectedTemplate,
        student_uuids: studentUuids,
        comment_type: templateForm.commentType,
    }

    await store
        .dispatch(initUrl + "applyTemplate", {
            form: payload,
        })
        .then((response) => {
            isLoading.value = false
            // Reset template form
            templateForm.selectedTemplate = ""
            // Refresh student data
            fetchStudent()
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const removeMark = async () => {
    if (!(await confirmAction())) {
        return
    }

    isLoading.value = true
    await store
        .dispatch(initUrl + "remove", {
            form: route.query,
        })
        .then((response) => {
            isLoading.value = false
            fetchStudent()
        })
        .catch((e) => {
            isLoading.value = false
        })
}

onMounted(async () => {
    await preRequisite()

    await fetchStudent()
})
</script>
