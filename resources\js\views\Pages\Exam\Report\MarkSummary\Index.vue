<template>
    <PageHeader
        :title="$trans(route.meta.label)"
        :navs="[
            { label: $trans('exam.exam'), path: 'Exam' },
            { label: $trans('exam.report.report'), path: 'ExamReport' },
        ]"
    >
        <PageHeaderAction
            url="exam/reports/mark-summary/"
            name="ExamReportMarkSummary"
            :title="$trans('exam.report.mark_summary.mark_summary')"
            :actions="userActions"
            :dropdown-actions="[]"
            @toggleFilter="showFilter = !showFilter"
        >
            <ResultDownloadButton
                v-if="hasValidFilters"
                result-type="mark-summary"
                :download-url="'/api/v1/app/exam/reports/mark-summary/download-pdf'"
                :show-dropdown="true"
                :show-print="true"
                :show-download="true"
                @print="onPrint"
                @download="onDownload"
                @error="onError"
            />
        </PageHeaderAction>
    </PageHeader>

    <ParentTransition appear :visibility="showFilter">
        <FilterForm
            @afterFilter="fetchReport"
            :init-url="initUrl"
            :pre-requisites="preRequisites"
            @hide="showFilter = false"
        ></FilterForm>
    </ParentTransition>

    <ParentTransition appear :visibility="true">
        <BaseCard no-padding no-content-padding :is-loading="isLoading">
        </BaseCard>
    </ParentTransition>
</template>

<script>
export default {
    name: "ExamReportMarkSummary",
}
</script>

<script setup>
import { ref, reactive, computed, onMounted } from "vue"
import { useRoute } from "vue-router"
import { useStore } from "vuex"
import FilterForm from "./Filter.vue"
import ResultDownloadButton from "@core/components/Ui/ResultDownloadButton.vue"

const route = useRoute()
const store = useStore()

const initUrl = "exam/report/"
const showFilter = ref(true)
const isLoading = ref(false)

const preRequisites = reactive({
    exams: [],
    attempts: [],
})

const hasValidFilters = computed(() => {
    return route.query.exam && route.query.batch && route.query.attempt
})

let userActions = ["filter"]

const preRequisite = async () => {
    isLoading.value = true
    await store
        .dispatch(initUrl + "preRequisite", { name: "mark-summary" })
        .then((response) => {
            isLoading.value = false
            Object.assign(preRequisites, response)
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const fetchReport = async () => {
    isLoading.value = true
    await store
        .dispatch(initUrl + "fetchReport", {
            name: "mark-summary",
            params: route.query,
        })
        .then((response) => {
            isLoading.value = false
            window.open("/print").document.write(response)
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const onPrint = (data) => {
    console.log('Print initiated:', data)
}

const onDownload = (data) => {
    console.log('Download initiated:', data)
}

const onError = (error) => {
    console.error('Action failed:', error)
}

onMounted(async () => {
    await preRequisite()

    // await fetchReport()
})
</script>
