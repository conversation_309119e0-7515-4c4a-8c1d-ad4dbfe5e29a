<?php

return [
    'errors' => [
        'select_all'  => 'Select All',
        'invalid_action' => 'This is not a valid action.',
        'invalid_operation' => 'This is not a valid operation.',
        'not_available' => 'Not Available, Please try again.',
        'invalid_input' => 'This is not a valid input.',
        'invalid_identifier' => 'Invalid identifier.',
        'duplicate_identifier' => 'Duplicate identifier found.',
        'validation_message_error' => 'The given data was invalid.',
        'feature_not_available' => 'This feature is not available.',
        'api_not_found' => 'This is not a valid API Endpoint.',
        'record_not_found' => 'No record found.',
        'attachment_not_found' => 'No attachment found.',
        'page_not_found' => 'This is not a valid page.',
        'page_not_found_title' => 'Page Not Found',
        'permission_denied_title' => 'Permission Denied',
        'not_supported_sms_driver' => 'This SMS gateway is not supported.',
        'unauthorized_title' => 'Unauthorized',
        'error_page_title' => 'Error Page',
        'restricted_test_mode_action' => 'This action is restricted in test mode.',
        'file_not_supported' => 'The file is not supported.',
        'file_too_large' => 'The file size is too large.',
        'file_does_not_exist' => 'The file doesn\'t exist.',
        'file_upload_limit_exceeded' => 'You have exceed max file upload limit.',
        'file_with_same_name_uploaded' => 'File with the same name already uploaded.',
        'file_not_found_title' => 'File not found!',
        'file_not_found_description' => 'The file you are looking for doesn\'t exist!',
        'something_wrong_with_form' => 'There are some error with the form, check and submit again.',
        'something_wrong_contact_author' => 'Something went wrong, please contact script author.',
        'under_maintenance' => 'The system is under maintenance.',
        '404_title' => 'Page not found!',
        '404_description' => 'The page you are looking for doesn\'t exist!',
        '403_title' => 'Unauthorised',
        '403_description' => 'You are not authorised to perform this action!',
        '401_title' => 'Unauthenticated!',
        '401_description' => 'You are not authenticated!',
        '503_title' => 'Service Unavailable',
        '503_description' => 'Please wait while we are updating the application.',
        'csrf_token_mismatch' => 'CSRF Token mismatch.',
        'max_period_in_days' => 'You can maximum choose :attribute days.',
        'import_error_message' => 'The given file has :attribute errors.',
        'max_import_limit_crossed' => 'You can import maximum :attribute records at a time.',
        'mail_send_error' => 'There is some error in sending email.',
        'login_required' => 'You must login to perform this action.',
        'feature_under_development' => 'This feature is under development.',
    ],
    'alerts' => [
        'title' => 'Are you sure?',
        'reset_description' => 'Your form will reset. Confirm to reset?',
        'description' => 'You might not be able to reverse this action. Confirm to proceed?',
        'delete_description' => 'You won\'t be able to reverse this action. Proceed to delete?',
        'are_your_sure' => 'Are you sure?',
        'confirm_submission' => 'Please confirm your submission.',
        'error_title' => 'Oops! Something is wrong.',
        'error_description' => 'Something went wrong, please try again.',
    ],
    'infos' => [
        'nothing_to_submit' => 'There is nothing to submit.',
        'loading' => 'Please wait while we load information for you.',
    ],
    'views' => [
        'card' => 'Card',
        'list' => 'List',
        'board' => 'Board',
    ],
    'download_pdf' => 'Download',
    'refresh' => 'Refresh',
    'greeting_message' => 'Welcome :name!',
    'under_maintenance' => 'Under Maintenance',
    'version_info' => 'Version :attribute',
    'import_error' => 'Import Error (:attribute)',
    'import_info' => 'Select excel file and click on upload button to import data.',
    'institute_info' => 'Info',
    'count' => 'Count',
    'user_account' => 'User Account',
    'period_between' => ':start to :end',
    'period_till' => ':start - Present',
    'select_no_option_text' => 'No result found, Type to search',
    'select_no_result_text' => 'No result found for your search.',
    'select_no_option' => 'No option found.',
    'ok' => 'OK',
    'yes' => 'Yes',
    'no' => 'No',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'days' => 'Days',
    'effective' => 'Effective',
    'not_effective' => 'Not Effective',
    'user_type' => 'User Type',
    'name' => 'Name',
    'option' => 'Option',
    'type' => 'Type',
    'since' => 'Since',
    'times' => 'Time(s)',
    'file' => 'File',
    'media' => 'Media',
    'data' => 'Data',
    'row' => 'Row',
    'demo' => 'Demo',
    'free' => 'Free',
    'card' => 'Card',
    'kanban' => 'Kanban',
    'preview' => 'Preview',
    'list' => 'List',
    'validate' => 'Validate',
    'download' => 'Download',
    'column' => 'Column',
    'color' => 'Color',
    'sidebar' => 'Sidebar',
    'general' => 'General',
    'basic' => 'Basic',
    'message' => 'Message',
    'search' => 'Search',
    'process' => 'Process',
    'proceed' => 'Proceed',
    'back' => 'Back',
    'import' => 'Import',
    'copy' => 'Copy',
    'sync' => 'Sync',
    'view' => 'View',
    'show' => 'Show',
    'cancel' => 'Cancel',
    'cancelled' => 'Cancelled',
    'rejected' => 'Rejected',
    'attachment' => 'Attachment',
    'recent' => 'Recent',
    'save' => 'Save',
    'submit' => 'Submit',
    'reorder' => 'Reorder',
    'loading' => 'Loading ...',
    'edit' => 'Edit',
    'reset' => 'Reset',
    'update' => 'Update',
    'assign' => 'Assign',
    'revoke' => 'Revoke',
    'remove' => 'Remove',
    'condition' => 'Condition',
    'sub_condition' => 'Sub Condition',
    'upload' => 'Upload',
    'change' => 'Change',
    'confirm' => 'Confirm',
    'image' => 'Image',
    'duplicate' => 'Duplicate',
    'total' => 'Total',
    'to' => 'to',
    'selected' => 'Selected',
    'view_log' => 'View Log',
    'times' => ':attribute time(s)',
    'percent' => 'Percent',
    'add' => 'Add',
    'delete' => 'Delete',
    'filter' => 'Filter',
    'print' => 'Print',
    'print_date_time' => 'Print Date & Time',
    'printed_at' => 'Printed At',
    'pdf' => 'Generate PDF',
    'excel' => 'Export to Excel',
    'move' => 'Move',
    'detail' => 'Detail',
    'record' => 'Record',
    'public' => 'Public',
    'other' => 'Other',
    'pin' => 'Pin',
    'unpin' => 'Unpin',
    'pinned' => 'Pinned',
    'unpinned' => 'Unpinned',
    'archive' => 'Archive',
    'unarchive' => 'Unarchive',
    'archived' => 'Archived',
    'keep_adding' => 'Keep Adding',
    'date' => 'Date',
    'time' => 'Time',
    'period' => 'Period',
    'code_number' => 'Code Number',
    'tag' => 'Tag',
    'tags' => 'Tags',
    'url' => 'URL',
    'default' => 'Default',
    'existing_tag' => 'Existing Tag',
    'tags_included' => 'Has Tags',
    'tags_excluded' => 'Doesn\'t Have Tags',
    'date_between' => 'Date Between',
    'created_by' => 'Created By',
    'created_at' => 'Created At',
    'updated_at' => 'Last Updated At',
    'imported_at' => 'Imported At',
    'bulk_action' => 'Bulk Action',
    'data_validated' => 'Your data is ready to import.',
    'unselect_all_records' => 'Unselect all records',
    'light_mode' => 'Light Mode',
    'dark_mode' => 'Dark Mode',
    'of' => 'Of',
    'by' => 'by',
    'at' => 'at',
    'new' => 'New',
    'all' => 'All',
    'from' => 'from',
    'more' => 'More',
    'sno' => 'S.No.',
    'failed' => 'Failed',
    'camera' => 'Camera',
    'start_camera' => 'Start Camera',
    'capture_image' => 'Capture Image',
    'opt_in' => 'Opt-in',
    'options' => 'Options',
    'report' => 'Report',
    'position' => 'Position',
    'ip' => 'IP Address',
    'user_agent' => 'User Agent',
    'enable_integration' => 'Enable Integration',
    'redirecting_to_payment_gateway' => 'Please wait while redirecting to payment gateway...',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'export_all' => 'Export All',
    'download_format' => 'Download Format',
    'export' => 'Export',
    'select' => 'Select',
    'select_all_on_page' => 'Select All on Page',
    'select_all_filtered' => 'Select All Filtered',
    'clear_global_selection' => 'Clear Global Selection',
    'all_filtered_items_selected' => 'All filtered items selected',
    'all_filtered' => 'All Filtered',
    'approved' => 'Approved',
    'pending' => 'Pending',
    'no_messages' => 'No messages yet',
    'stop' => 'Stop',
    'error_occurred' => 'An error occurred',
    'confirm_delete' => 'Are you sure you want to delete this item?',
    'country' => [
        'country' => 'Country',
        'props' => [
            'name' => 'Name',
            'alt_code' => 'Alt Code',
            'code' => 'Code',
            'currency' => 'Currency',
            'region' => 'Region',
            'emoji' => 'Emoji',
        ],
    ],
    'history' => 'History',
    'state' => [
        'state' => 'State',
        'props' => [
            'name' => 'Name',
        ],
    ],
];
