<?php

namespace App\Services\Exam;

use App\Models\Academic\Batch;
use App\Models\Exam\Exam;
use App\Models\Exam\Term;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ResultPdfService
{
    /**
     * Generate PDF for marksheet (exam-wise, term-wise, cumulative)
     */
    public function generateMarksheetPdf(Request $request, array $data)
    {
        $type = $request->type;
        $batch = $data['batch'];
        $exam = $data['exam'] ?? null;
        $term = $data['term'] ?? null;
        $template = $data['template'] ?? 'default';
        
        // Determine view path based on type
        $viewPath = $this->getMarksheetViewPath($type, $template);
        
        // Render content
        $content = view()->first([
            config('config.print.custom_path') . $viewPath,
            'print.' . $viewPath
        ], $data)->render();

        // Generate filename
        $filename = $this->generateMarksheetFilename($type, $batch, $exam, $term);

        return $this->generatePdf($content, $filename, $request);
    }

    /**
     * Generate PDF for exam summary reports
     */
    public function generateExamSummaryPdf(Request $request, array $data)
    {
        $exam = $data['exam'];
        $batch = $data['batch'];
        
        $content = view()->first([
            config('config.print.custom_path') . 'exam.report.exam-summary',
            'print.exam.report.exam-summary'
        ], $data)->render();

        $filename = Str::slug($exam->name . '-' . $batch->name . '-exam-summary') . '.pdf';

        return $this->generatePdf($content, $filename, $request);
    }

    /**
     * Generate PDF for marksheet print (legacy)
     */
    public function generateMarksheetPrintPdf(Request $request, array $data)
    {
        $batch = $data['batch'];
        $exam = $data['exam'] ?? null;
        $template = $data['template'] ?? 'default';
        
        $content = view()->first([
            config('config.print.custom_path') . 'exam.marksheet.' . $template,
            'print.exam.marksheet.' . $template
        ], $data)->render();

        // Generate filename based on type
        $type = $request->type ?? 'exam_wise';
        $filename = $this->generateMarksheetFilename($type, $batch, $exam);

        return $this->generatePdf($content, $filename, $request);
    }

    /**
     * Core PDF generation method following existing patterns
     */
    private function generatePdf(string $content, string $filename, Request $request)
    {
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => $request->paper_size ?? 'A4-L', // Default to landscape A4
            'margin_left' => 10,
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);
        
        // Support unicode characters
        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont = true;
        $mpdf->WriteHTML($content);

        // Output the PDF for download
        return $mpdf->Output($filename, 'D');
    }

    /**
     * Get view path for marksheet based on type and template
     */
    private function getMarksheetViewPath(string $type, string $template): string
    {
        switch ($type) {
            case 'cumulative':
                return 'exam.cumulative-marksheet';
            case 'term_wise':
                return 'exam.term-wise-marksheet';
            case 'exam_wise_cameroon':
                return 'exam.exam-wise-cameroon-marksheet';
            case 'term_wise_cameroon':
                return 'exam.term-wise-cameroon-marksheet';
            case 'exam_wise_credit_based':
                return 'exam.exam-wise-credit-based-marksheet';
            default:
                return 'exam.exam-wise-marksheet';
        }
    }

    /**
     * Generate filename for marksheet PDFs
     */
    private function generateMarksheetFilename(string $type, $batch, $exam = null, $term = null): string
    {
        $parts = [];
        
        if ($batch) {
            $parts[] = $batch->name ?? $batch;
        }
        
        if ($exam) {
            $parts[] = $exam->name ?? $exam;
        }
        
        if ($term) {
            $parts[] = $term->name ?? $term;
        }
        
        $parts[] = str_replace('_', '-', $type);
        $parts[] = 'marksheet';
        
        return Str::slug(implode('-', array_filter($parts))) . '.pdf';
    }

    /**
     * Get standard PDF configuration
     */
    public function getStandardPdfConfig(Request $request): array
    {
        return [
            'mode' => 'utf-8',
            'format' => $request->paper_size ?? 'A4-L',
            'margin_left' => 10,
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ];
    }

    /**
     * Get available paper sizes
     */
    public function getAvailablePaperSizes(): array
    {
        return [
            'A4-P' => 'A4 Portrait',
            'A4-L' => 'A4 Landscape',
            'A3-P' => 'A3 Portrait',
            'A3-L' => 'A3 Landscape',
            'Letter-P' => 'Letter Portrait',
            'Letter-L' => 'Letter Landscape',
            'Legal-P' => 'Legal Portrait',
            'Legal-L' => 'Legal Landscape',
        ];
    }
}
