<?php

namespace App\Services\Exam\Report;

use App\Enums\Exam\AssessmentAttempt;
use App\Http\Resources\Exam\ExamResource;
use App\Models\Academic\Batch;
use App\Models\Academic\Subject;
use App\Models\Academic\SubjectRecord;
use App\Models\Exam\Exam;
use App\Models\Exam\Grade;
use App\Models\Exam\Schedule;
use App\Models\Exam\Term;
use App\Models\Student\Student;
use App\Models\Student\SubjectWiseStudent;
use App\Actions\Student\FetchBatchWiseStudent;
use App\Support\HasGrade;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Enum;

class CumulativeMarkSummaryService
{
    use HasGrade;

    public function preRequisite(): array
    {
        $terms = Term::query()
            ->with('division')
            ->byPeriod()
            ->get();

        $attempts = AssessmentAttempt::getOptions();

        return compact('terms', 'attempts');
    }

    public function fetchReport(Request $request)
    {
        $request->validate([
            'attempt' => ['required', new Enum(AssessmentAttempt::class)],
            'batch' => 'required|uuid',
            'terms' => 'nullable',  // Allow both array and string/null
            'title' => 'string|nullable|max:255',
            'signatory_1' => 'string|nullable|max:255',
            'signatory_2' => 'string|nullable|max:255',
            'signatory_3' => 'string|nullable|max:255',
            'signatory_4' => 'string|nullable|max:255',
        ]);

        // Normalize terms to array if it's a string or single value
        $termsInput = $request->input('terms');
        if (is_string($termsInput)) {
            $termsInput = [$termsInput];
        } elseif (!is_array($termsInput)) {
            $termsInput = [];
        }

        // Validate individual term UUIDs if provided
        if (!empty($termsInput)) {
            foreach ($termsInput as $term) {
                if (!is_string($term) || !preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $term)) {
                    throw new \InvalidArgumentException('Invalid term UUID format');
                }
            }
        }

        $batch = Batch::query()
            ->byPeriod()
            ->filterAccessible()
            ->whereUuid($request->batch)
            ->getOrFail(trans('academic.batch.batch'), 'batch');

        // Get terms - if specific terms are selected, use those, otherwise use all terms
        $termsQuery = Term::query()
            ->with([
                'exams' => function ($q) use ($batch) {
                    $q->with('schedules.assessment')
                        ->whereHas('schedules', function ($q) use ($batch) {
                            $q->where('batch_id', $batch->id);
                        })->orderBy('position');
                },
            ])
            ->byPeriod()
            ->where(function ($q) use ($batch) {
                $q->whereDivisionId($batch->course->division_id)
                    ->orWhereNull('division_id');
            });

        if (!empty($termsInput)) {
            $termsQuery->whereIn('uuid', $termsInput);
        }

        $terms = $termsQuery->get();

        if ($terms->isEmpty()) {
            throw new \Exception(trans('exam.term.no_terms_found'));
        }

        // Collect all exams from all terms
        $allExams = collect();
        foreach ($terms as $term) {
            $allExams = $allExams->merge($term->exams);
        }

        if ($allExams->isEmpty()) {
            throw new \Exception(trans('exam.exam.no_exams_found'));
        }

        // Get all schedules for the batch and attempt
        $schedules = Schedule::query()
            ->with(['grade', 'assessment', 'records.subject'])
            ->whereIn('exam_id', $allExams->pluck('id'))
            ->whereBatchId($batch->id)
            ->where('attempt', $request->attempt)
            ->get();

        if ($schedules->isEmpty()) {
            throw new \Exception(trans('exam.schedule.no_schedules_found'));
        }

        // Get all subject records
        $subjectRecords = SubjectRecord::query()
            ->where(function ($q) use ($batch) {
                $q->where('course_id', $batch->course_id)
                    ->orWhere('batch_id', $batch->id);
            })
            ->whereIn('subject_id', $schedules->flatMap->records->pluck('subject_id')->unique())
            ->get();

        $subjectWiseStudents = SubjectWiseStudent::query()
            ->whereBatchId($batch->id)
            ->get();

        $params = [
            'batch' => $batch->uuid,
            'subject_wise_students' => $subjectWiseStudents,
            'select_all' => true,
        ];

        $inputSubjects = Subject::query()
            ->byPeriod()
            ->whereIn('uuid', Str::toArray($request->query('subjects')))
            ->get();

        $students = (new FetchBatchWiseStudent)->execute($params);

        if ($students->isEmpty()) {
            throw new \Exception(trans('student.student.no_students_found'));
        }

        // Follow MarkSummaryService pattern exactly
        $grade = $schedules->first()?->grade;
        if (!$grade) {
            throw new \Exception(trans('exam.grade.no_grade_found'));
        }

        $failGrades = collect($grade->records)->where('is_fail_grade', true)->pluck('code')->toArray();
        $params['fail_grades'] = $failGrades;

        // Transform to subjects array following MarkSummaryService pattern
        $subjects = $this->updateSubjects($schedules, $subjectRecords, $terms, $inputSubjects, $params);

        if (empty($subjects)) {
            throw new \Exception(trans('exam.no_records_found'));
        }

        // Filter subjects based on actual data availability (like exam-wise reports)
        $subjects = $this->filterSubjectsWithNoData($subjects, $students, $params);

        // 3-row header structure for cumulative mark summary
        $header = [];
        $termHeader = [];
        $subHeader = [];
        $marks = [];

        // Add basic headers (3-row structure)
        array_push($header, ['key' => 'sno', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('general.sno')]);
        array_push($header, ['key' => 'roll_number', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('student.roll_number.roll_number')]);
        array_push($header, ['key' => 'name', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('student.props.name')]);

        // Update subjects to create 3-row headers and marks
        [$header, $termHeader, $subHeader, $marks] = $this->updateSubjectsWithTerms($header, $termHeader, $subHeader, $marks, $subjects, $terms);

        // Add term totals columns (one for each term)
        foreach ($terms as $term) {
            array_push($header, ['key' => "term_{$term->id}_total", 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => $term->name . ' Total']);
        }

        // Add overall columns (3-row structure)
        array_push($header, ['key' => 'total', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('exam.total')]);
        array_push($header, ['key' => 'percent', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('exam.percentage')]);
        //array_push($header, ['key' => 'grade', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('exam.grade.grade')]);

        // Generate student rows with cumulative structure
        $marks = collect($marks);
        $rows = [];

        $mainSubjects = collect($subjects)->filter(function ($subject) {
            return !$subject['has_grading'];
        })->toArray();

        $i = 0;
        foreach ($students as $student) {
            $row = [];

            $row = $this->getCumulativeStudentMarks($student, $grade, $marks, $mainSubjects, $terms, $row, $params);

            if (collect($row)->filter(function ($row) {
                return Arr::get($row, 'type') == 'marks' && Arr::get($row, 'label') == '';
            })->count() == count($row)) {
                continue;
            }

            $i++;

            array_unshift($row, ['key' => 'name', 'label' => $student->name.' ('.$student->code_number.')']);
            array_unshift($row, ['key' => 'roll_number', 'label' => $student->roll_number]);
            array_unshift($row, ['key' => 'sno', 'label' => $i]);

            $hasFailGrade = collect($row)->filter(function ($row) use ($failGrades) {
                return in_array(Arr::get($row, 'grade'), $failGrades);
            })->count();

            $studentMaxMarkTotal = collect($row)->filter(function ($row) {
                return Arr::get($row, 'type') == 'marks';
            })->sum('max_mark');

            $studentTotal = collect($row)->filter(function ($row) {
                return Arr::get($row, 'type') == 'marks';
            })->sum('numeric_mark');

            // Calculate and add term totals for each term
            foreach ($terms as $term) {
                $termTotal = $this->calculateStudentTermTotal($row, $subjects, $term);
                $row[] = ['key' => "term_{$term->id}_total", 'class' => 'text-center', 'label' => $termTotal];
            }

            $row[] = ['key' => 'total', 'class' => 'text-center', 'label' => $studentTotal];

            $studentPercent = $studentMaxMarkTotal > 0 ? round(($studentTotal / $studentMaxMarkTotal) * 100, 2) : 0;

            $row[] = ['key' => 'percent', 'class' => 'text-center', 'label' => $studentPercent];

            //$studentGrade = $this->getGrade($grade, $studentMaxMarkTotal, $studentTotal);

            //$row[] = ['key' => 'grade', 'class' => 'text-center', 'label' => $studentGrade, 'text-style' => $hasFailGrade ? 'circular-border' : ''];

            $rows[] = $row;
        }

        // Calculate positions following MarkSummaryService pattern
        $this->calculatePositions($rows, $subjects, $terms);

        // Sort students by overall total (highest to lowest) - this is the main sorting criteria
        usort($rows, function($a, $b) {
            $totalA = collect($a)->firstWhere('key', 'total')['label'] ?? 0;
            $totalB = collect($b)->firstWhere('key', 'total')['label'] ?? 0;

            // Sort by total (descending - highest first)
            if ($totalA != $totalB) {
                return $totalB <=> $totalA;
            }

            // If totals are equal, sort by percentage (descending)
            $percentA = collect($a)->firstWhere('key', 'percent')['label'] ?? 0;
            $percentB = collect($b)->firstWhere('key', 'percent')['label'] ?? 0;
            return $percentB <=> $percentA;
        });

        // Update serial numbers to reflect the new sorted order (highest total gets rank 1)
        foreach ($rows as $index => $row) {
            foreach ($row as $key => $item) {
                if ($item['key'] === 'sno') {
                    $rows[$index][$key]['label'] = $index + 1;
                    break;
                }
            }
        }

        // Get layout and titles
        $layout = $this->getLayout($request);
        $titles = $this->getTitles($request, $batch, $terms);

        // Return view (3-row structure)
        if ($request->action === 'pdf') {
            return $this->generatePdf($request, $titles, $rows, $header, $termHeader, $subHeader, $layout, $batch, $terms);
        }

        return view('print.exam.report.cumulative-mark-summary', compact('titles', 'rows', 'header', 'termHeader', 'subHeader', 'layout', 'request'))->render();
    }

    private function updateSubjects(Collection $schedules, Collection $subjectRecords, Collection $terms, Collection $inputSubjects, array $params)
    {
        $subjects = [];

        // Group schedules by subject (following MarkSummaryService pattern)
        $subjectGroups = [];
        foreach ($schedules as $schedule) {
            foreach ($schedule->records as $record) {
                $subject = $record->subject;
                if (!$subject) continue;

                $hasExam = $record->getConfig('has_exam');
                if (!$hasExam) continue;

                // Filter by inputSubjects if provided (like MarkSummaryService)
                if ($inputSubjects->isNotEmpty() && !$inputSubjects->contains('id', $subject->id)) {
                    continue;
                }

                $subjectId = $record->subject_id;
                $termId = $schedule->exam->term_id;

                if (!isset($subjectGroups[$subjectId])) {
                    $subjectGroups[$subjectId] = [
                        'subject' => $subject,
                        'subject_record' => $subjectRecords->firstWhere('subject_id', $subjectId),
                        'terms' => [],
                    ];
                }

                $assessments = $record->getConfig('assessments', []);
                $recordMarks = $record->marks ?? [];

                $subjectGroups[$subjectId]['terms'][$termId] = [
                    'assessments' => $assessments,
                    'marks' => $recordMarks,
                    'not_applicable_students' => $record->getConfig('not_applicable_students', []),
                ];
            }
        }

        // Transform to subjects array (following MarkSummaryService pattern)
        foreach ($subjectGroups as $subjectId => $subjectGroup) {
            $subject = $subjectGroup['subject'];
            $subjectRecord = $subjectGroup['subject_record'];

            $allAssessments = [];
            $allMarks = [];
            $allNotApplicableStudents = [];

            // Add term-wise assessments
            foreach ($terms as $term) {
                if (!isset($subjectGroup['terms'][$term->id])) continue;

                $termData = $subjectGroup['terms'][$term->id];
                foreach ($termData['assessments'] as $assessment) {
                    $termCode = $term->name . '_' . $assessment['code'];
                    $allAssessments[] = [
                        'code' => $termCode,
                        'name' => $term->name . ' ' . ($assessment['name'] ?? $assessment['code']),
                        'max_mark' => $assessment['max_mark'],
                        'term_id' => $term->id,
                        'original_code' => $assessment['code'],
                    ];

                    $assessmentMarks = collect($termData['marks'])->firstWhere('code', $assessment['code']);
                    if ($assessmentMarks) {
                        $allMarks[] = [
                            'code' => $termCode,
                            'marks' => $assessmentMarks['marks'] ?? [],
                        ];
                    }
                }
                $allNotApplicableStudents = array_merge($allNotApplicableStudents, $termData['not_applicable_students']);
            }

            // Calculate aggregate assessments with proper max_mark totals
            $aggregateAssessments = [];
            foreach ($subjectGroup['terms'] as $termData) {
                foreach ($termData['assessments'] as $assessment) {
                    $code = $assessment['code'];
                    if (!isset($aggregateAssessments[$code])) {
                        $aggregateAssessments[$code] = [
                            'code' => $code,
                            'name' => $assessment['name'] ?? $assessment['code'],
                            'max_mark' => 0,
                            'original_code' => $code,
                        ];
                    }
                    $aggregateAssessments[$code]['max_mark'] += $assessment['max_mark'];
                }
            }

            // Skip adding individual aggregate assessments since we only show total + final(%) in aggregate column

            $subjects[] = [
                'id' => $subjectId,
                'name' => $subject->name,
                'shortcode' => $subject->shortcode,
                'assessments' => $allAssessments,
                'position' => $subject->position,
                'total' => collect($allAssessments)->sum('max_mark'),
                'marks' => $allMarks,
                'not_applicable_students' => array_unique($allNotApplicableStudents),
                'is_elective' => (bool) $subjectRecord?->is_elective,
                'has_grading' => (bool) $subjectRecord?->has_grading,
                'terms' => $subjectGroup['terms'],
            ];
        }

        return collect($subjects)->sortBy('position')->toArray();
    }

    private function createTabularHeaders(array $subjects, Collection $terms)
    {
        $header = [];
        $subHeader = [];
        $termHeader = [];

        // Add basic headers (3-row structure for term breakdown)
        array_push($header, ['key' => 'sno', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('general.sno')]);
        array_push($header, ['key' => 'roll_number', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('student.roll_number.roll_number')]);
        array_push($header, ['key' => 'name', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('student.props.name')]);

        foreach ($subjects as $subject) {
            $subjectId = $subject['id'];

            // Calculate total colspan for this subject
            $termColspans = [];
            $totalColspan = 0;

            foreach ($terms as $term) {
                if (!isset($subject['terms'][$term->id])) {
                    continue;
                }

                $termData = $subject['terms'][$term->id];
                $termAssessmentCount = count($termData['assessments']);

                $termColspan = $termAssessmentCount + 2; // +1 for total, +1 for position
                $termColspans[$term->id] = $termColspan;
                $totalColspan += $termColspan;
            }

            // Add aggregate colspan (assessments + total + final score + position)
            $aggregateAssessments = [];
            foreach ($subject['terms'] as $termData) {
                foreach ($termData['assessments'] as $assessment) {
                    $aggregateAssessments[$assessment['code']] = $assessment;
                }
            }
            $aggregateColspan = count($aggregateAssessments) + 3; // +1 for total, +1 for final score, +1 for position
            $totalColspan += $aggregateColspan;

            // Add subject header (Row 1)
            array_push($header, [
                'key' => "subject{$subjectId}",
                'class' => 'text-center font-weight-bold',
                'colspan' => $totalColspan,
                'label' => $subject['shortcode'] ?: $subject['name'],
            ]);

            // Add term headers (Row 2)
            foreach ($terms as $term) {
                if (isset($termColspans[$term->id])) {
                    array_push($termHeader, [
                        'key' => "subject{$subjectId}_term{$term->id}",
                        'class' => 'text-center font-weight-bold',
                        'colspan' => $termColspans[$term->id],
                        'label' => $term->name,
                    ]);
                }
            }

            // Add aggregate header (Row 2)
            array_push($termHeader, [
                'key' => "subject{$subjectId}_aggregate",
                'class' => 'text-center font-weight-bold',
                'colspan' => $aggregateColspan,
                'label' => 'Aggregate',
            ]);

            // Add assessment sub-headers (Row 3)
            foreach ($terms as $term) {
                if (!isset($subject['terms'][$term->id])) {
                    continue;
                }

                $termData = $subject['terms'][$term->id];

                // Add assessment columns for this term
                foreach ($termData['assessments'] as $assessment) {
                    array_push($subHeader, [
                        'key' => "subject{$subjectId}_term{$term->id}_{$assessment['code']}",
                        'class' => 'text-center font-weight-bold',
                        'label' => $assessment['code'] . '(' . $assessment['max_mark'] . ')',
                        'text' => $term->name . ' ' . ($assessment['name'] ?? $assessment['code']),
                    ]);
                }

                // Add total and position for this term
                $termTotal = collect($termData['assessments'])->sum('max_mark');
                array_push($subHeader, [
                    'key' => "subject{$subjectId}_term{$term->id}_total",
                    'class' => 'text-center font-weight-bold',
                    'label' => 'Total(' . $termTotal . ')',
                    'text' => $term->name . ' Total',
                ]);

                array_push($subHeader, [
                    'key' => "subject{$subjectId}_term{$term->id}_position",
                    'class' => 'text-center font-weight-bold',
                    'label' => 'Pos',
                    'text' => $term->name . ' Position',
                ]);
            }

            // Calculate aggregate assessments (sum across all terms)
            $aggregateAssessments = [];
            foreach ($subject['terms'] as $termData) {
                foreach ($termData['assessments'] as $assessment) {
                    $code = $assessment['code'];
                    if (!isset($aggregateAssessments[$code])) {
                        $aggregateAssessments[$code] = $assessment;
                        $aggregateAssessments[$code]['max_mark'] = 0;
                    }
                    $aggregateAssessments[$code]['max_mark'] += $assessment['max_mark'];
                }
            }

            // Add aggregate assessment columns
            foreach ($aggregateAssessments as $assessment) {
                array_push($subHeader, [
                    'key' => "subject{$subjectId}_agg_{$assessment['code']}",
                    'class' => 'text-center font-weight-bold',
                    'label' => $assessment['code'] . '(' . $assessment['max_mark'] . ')',
                    'text' => 'Total ' . ($assessment['name'] ?? $assessment['code']),
                ]);
            }

            // Add aggregate total, final score, and position
            $aggregateTotal = collect($aggregateAssessments)->sum('max_mark');
            array_push($subHeader, [
                'key' => "subject{$subjectId}_agg_total",
                'class' => 'text-center font-weight-bold',
                'label' => 'Total(' . $aggregateTotal . ')',
                'text' => 'Aggregate Total',
            ]);

            array_push($subHeader, [
                'key' => "subject{$subjectId}_agg_final_score",
                'class' => 'text-center font-weight-bold',
                'label' => 'Final(%)',
                'text' => 'Final Score',
            ]);

            array_push($subHeader, [
                'key' => "subject{$subjectId}_agg_position",
                'class' => 'text-center font-weight-bold',
                'label' => 'Pos',
                'text' => 'Aggregate Position',
            ]);
        }

        // Add overall columns
        array_push($header, ['key' => 'total', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('exam.total')]);
        array_push($header, ['key' => 'percent', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('exam.percentage')]);
        //array_push($header, ['key' => 'grade', 'rowspan' => 3, 'class' => 'font-weight-bold', 'label' => trans('exam.grade.grade')]);

        return [$header, $subHeader, $termHeader];


    }

    private function updateSubjectsWithTerms(array $header, array $termHeader, array $subHeader, array $marks, array $subjects, Collection $terms)
    {
        foreach ($subjects as $subject) {
            $subjectId = $subject['id'];

            // Calculate total colspan for this subject (3-row structure)
            $totalColspan = 0;
            $termColspans = [];

            // Calculate colspan for each term
            foreach ($terms as $term) {
                if (!isset($subject['terms'][$term->id])) continue;

                $termData = $subject['terms'][$term->id];
                $termAssessmentCount = count($termData['assessments']);
                $termColspan = $termAssessmentCount + 2; // +1 for total, +1 for position
                $termColspans[$term->id] = $termColspan;
                $totalColspan += $termColspan;
            }

            // Calculate colspan for aggregate (only total + final(%) + position)
            $aggregateColspan = 3; // total + final(%) + position
            $totalColspan += $aggregateColspan;

            // Row 1: Subject header
            array_push($header, [
                'key' => "subject{$subjectId}",
                'class' => 'text-center font-weight-bold',
                'colspan' => $totalColspan,
                'label' => $subject['shortcode'] ?: $subject['name'],
            ]);

            // Row 2: Term headers (Term1, Term2, Term3, Aggregate)
            foreach ($terms as $term) {
                if (isset($termColspans[$term->id])) {
                    array_push($termHeader, [
                        'key' => "subject{$subjectId}_term{$term->id}",
                        'class' => 'text-center font-weight-bold',
                        'colspan' => $termColspans[$term->id],
                        'label' => $term->name,
                    ]);
                }
            }

            // Add Aggregate header to Row 2
            array_push($termHeader, [
                'key' => "subject{$subjectId}_aggregate",
                'class' => 'text-center font-weight-bold',
                'colspan' => $aggregateColspan,
                'label' => 'Aggregate',
            ]);

            // Row 3: Assessment breakdown
            // For each term: add individual assessments + total + position
            foreach ($terms as $term) {
                if (!isset($subject['terms'][$term->id])) continue;

                $termData = $subject['terms'][$term->id];

                foreach ($termData['assessments'] as $assessment) {
                    $termCode = $term->name . '_' . $assessment['code'];
                    array_push($subHeader, [
                        'key' => "subject{$subjectId}_{$termCode}",
                        'class' => 'text-center font-weight-bold',
                        'label' => $assessment['code'] . '(' . $assessment['max_mark'] . ')',
                        'text' => $term->name . ' ' . ($assessment['name'] ?? $assessment['code']),
                    ]);

                    // Add to marks array
                    $assessmentMarks = collect($subject['marks'])->firstWhere('code', $termCode);
                    $marks[] = [
                        'subject_id' => $subjectId,
                        'assessment_code' => $termCode,
                        'max_mark' => $assessment['max_mark'],
                        'marks' => $assessmentMarks['marks'] ?? [],
                    ];
                }

                // Add term total
                $termTotal = collect($termData['assessments'])->sum('max_mark');
                array_push($subHeader, [
                    'key' => "subject{$subjectId}_{$term->name}_total",
                    'class' => 'text-center font-weight-bold',
                    'label' => 'Total(' . $termTotal . ')',
                    'text' => $term->name . ' Total',
                ]);

                // Add term position
                array_push($subHeader, [
                    'key' => "subject{$subjectId}_{$term->name}_position",
                    'class' => 'text-center font-weight-bold',
                    'label' => 'Pos',
                    'text' => $term->name . ' Position',
                ]);
            }

            // For aggregate: only add total + final(%) + position (no individual assessments)
            // Calculate aggregate total from all terms
            $aggregateTotal = 0;
            foreach ($terms as $term) {
                if (isset($subject['terms'][$term->id])) {
                    $termData = $subject['terms'][$term->id];
                    $aggregateTotal += collect($termData['assessments'])->sum('max_mark');
                }
            }

            // Add aggregate total
            array_push($subHeader, [
                'key' => "subject{$subjectId}_aggregate_total",
                'class' => 'text-center font-weight-bold',
                'label' => 'Total(' . $aggregateTotal . ')',
                'text' => 'Aggregate Total',
            ]);

            // Add final percentage
            array_push($subHeader, [
                'key' => "subject{$subjectId}_final_percent",
                'class' => 'text-center font-weight-bold',
                'label' => 'Final(%)',
                'text' => 'Final Percentage',
            ]);

            // Add aggregate position
            array_push($subHeader, [
                'key' => "subject{$subjectId}_aggregate_position",
                'class' => 'text-center font-weight-bold',
                'label' => 'Pos',
                'text' => 'Aggregate Position',
            ]);
        }

        return [$header, $termHeader, $subHeader, $marks];
    }

    private function generateStudentRows(Collection $students, Grade $grade, Collection $marks, array $subjects, array $params)
    {
        $rows = [];
        $failGrades = collect($grade->records)->where('is_fail_grade', true)->pluck('code')->toArray();

        $mainSubjects = collect($subjects)->filter(function ($subject) {
            return !$subject['has_grading'];
        })->toArray();

        $gradingSubjects = collect($subjects)->filter(function ($subject) {
            return $subject['has_grading'];
        })->toArray();

        $i = 0;
        foreach ($students as $student) {
            $studentTotal = 0;
            $row = [];

            $row = $this->getStudentMarks($student, $grade, $marks, $mainSubjects, $row, $params);

            if (collect($row)->filter(function ($row) {
                return Arr::get($row, 'type') == 'marks' && Arr::get($row, 'label') == '';
            })->count() == count($row)) {
                continue;
            }

            $i++;

            array_unshift($row, ['key' => 'name', 'label' => $student->name.' ('.$student->code_number.')']);
            array_unshift($row, ['key' => 'roll_number', 'label' => $student->roll_number]);
            array_unshift($row, ['key' => 'sno', 'label' => $i]);

            // $hasFailGrade = collect($row)->filter(function ($row) use ($failGrades) {
            //     return in_array(Arr::get($row, 'grade'), $failGrades);
            // })->count();

            $studentMaxMarkTotal = collect($row)->filter(function ($row) {
                return Arr::get($row, 'type') == 'marks';
            })->sum('max_mark');

            $studentTotal = collect($row)->filter(function ($row) {
                return Arr::get($row, 'type') == 'marks';
            })->sum('numeric_mark');

            $row[] = ['key' => 'total', 'class' => 'text-center', 'label' => $studentTotal];

            $studentPercent = $studentMaxMarkTotal > 0 ? round(($studentTotal / $studentMaxMarkTotal) * 100, 2) : 0;

            $row[] = ['key' => 'percent', 'class' => 'text-center', 'label' => $studentPercent];

            //$studentGrade = $this->getGrade($grade, $studentMaxMarkTotal, $studentTotal);

            //$row[] = ['key' => 'grade', 'class' => 'text-center', 'label' => $studentGrade, 'text-style' => $hasFailGrade ? 'circular-border' : ''];

            $rows[] = $row;
        }

        return $rows;
    }





    private function calculatePositions(array &$rows, array $subjects, Collection $terms)
    {
        foreach ($subjects as $subject) {
            $subjectId = $subject['id'];
            $hasGrading = $subject['has_grading'] ?? false;

            if ($hasGrading) {
                continue; // Skip grading subjects
            }

            // Calculate term positions for each term
            foreach ($terms as $term) {
                $this->calculateTermPositions($rows, $subjectId, $term->name);
            }

            // Calculate aggregate positions
            $this->calculateAggregatePositions($rows, $subjectId);
        }
    }

    private function calculateSubjectPositions(array &$rows, int $subjectId)
    {
        $subjectMarks = [];

        // Collect all student marks for this subject
        foreach ($rows as $index => $row) {
            $subjectTotal = collect($row)->firstWhere('key', "subject{$subjectId}_total");
            if ($subjectTotal && $subjectTotal['label'] !== '-' && isset($subjectTotal['numeric_mark'])) {
                $subjectMarks[] = [
                    'student_index' => $index,
                    'total' => $subjectTotal['numeric_mark'],
                ];
            }
        }

        // Sort by total marks (descending)
        usort($subjectMarks, function($a, $b) {
            return $b['total'] <=> $a['total'];
        });

        // Assign positions
        $currentPosition = 1;
        for ($i = 0; $i < count($subjectMarks); $i++) {
            if ($i > 0 && $subjectMarks[$i]['total'] < $subjectMarks[$i-1]['total']) {
                $currentPosition = $i + 1;
            }

            $studentIndex = $subjectMarks[$i]['student_index'];

            // Update the position in the rows
            foreach ($rows[$studentIndex] as $key => $item) {
                if ($item['key'] === "subject{$subjectId}_position") {
                    $rows[$studentIndex][$key]['label'] = $currentPosition;
                    break;
                }
            }
        }
    }

    private function calculateTermPositions(array &$rows, int $subjectId, string $termName)
    {
        $termMarks = [];

        // Collect all student marks for this subject's term total
        foreach ($rows as $index => $row) {
            $termTotal = collect($row)->firstWhere('key', "subject{$subjectId}_{$termName}_total");
            if ($termTotal && $termTotal['label'] !== '-' && isset($termTotal['numeric_mark'])) {
                $termMarks[] = [
                    'student_index' => $index,
                    'total' => $termTotal['numeric_mark'],
                ];
            }
        }

        // Sort by total marks (descending)
        usort($termMarks, function($a, $b) {
            return $b['total'] <=> $a['total'];
        });

        // Assign positions
        $currentPosition = 1;
        for ($i = 0; $i < count($termMarks); $i++) {
            if ($i > 0 && $termMarks[$i]['total'] < $termMarks[$i-1]['total']) {
                $currentPosition = $i + 1;
            }

            $studentIndex = $termMarks[$i]['student_index'];

            // Update the position in the rows
            foreach ($rows[$studentIndex] as $key => $item) {
                if ($item['key'] === "subject{$subjectId}_{$termName}_position") {
                    $rows[$studentIndex][$key]['label'] = $currentPosition;
                    break;
                }
            }
        }
    }

    private function calculateAggregatePositions(array &$rows, int $subjectId)
    {
        $aggregateMarks = [];

        // Collect all student marks for this subject's aggregate total
        foreach ($rows as $index => $row) {
            $aggregateTotal = collect($row)->firstWhere('key', "subject{$subjectId}_aggregate_total");
            if ($aggregateTotal && $aggregateTotal['label'] !== '-' && isset($aggregateTotal['numeric_mark'])) {
                $aggregateMarks[] = [
                    'student_index' => $index,
                    'total' => $aggregateTotal['numeric_mark'],
                ];
            }
        }

        // Sort by total marks (descending)
        usort($aggregateMarks, function($a, $b) {
            return $b['total'] <=> $a['total'];
        });

        // Assign positions
        $currentPosition = 1;
        for ($i = 0; $i < count($aggregateMarks); $i++) {
            if ($i > 0 && $aggregateMarks[$i]['total'] < $aggregateMarks[$i-1]['total']) {
                $currentPosition = $i + 1;
            }

            $studentIndex = $aggregateMarks[$i]['student_index'];

            // Update the position in the rows
            foreach ($rows[$studentIndex] as $key => $item) {
                if ($item['key'] === "subject{$subjectId}_aggregate_position") {
                    $rows[$studentIndex][$key]['label'] = $currentPosition;
                    break;
                }
            }
        }
    }

    private function getStudentMarks(Student $student, Grade $grade, Collection $marks, array $subjects, array $row, array $params = [])
    {
        $subjectWiseStudents = Arr::get($params, 'subject_wise_students', collect([]));
        $failGrades = Arr::get($params, 'fail_grades', []);

        foreach ($subjects as $subject) {
            $notApplicableStudents = $subject['not_applicable_students'] ?? [];
            $subjectId = $subject['id'];
            $subjectTotal = Arr::get($subject, 'total', 0);

            $isElective = false;

            if (Arr::get($subject, 'is_elective')) {
                $isElective = $subjectWiseStudents
                    ->where('student_id', $student->id)
                    ->where('subject_id', $subject['id'])
                    ->first() ? true : false;
            }

            if (in_array($student->uuid, $notApplicableStudents)) {
                foreach ($subject['assessments'] as $assessment) {
                    $row[] = [
                        'subject_id' => $subject['id'],
                        'assessment_code' => $assessment['code'],
                        'key' => "subject{$subject['id']}_{$assessment['code']}",
                        'label' => '-',
                        'class' => 'text-center',
                        'max_mark' => 0,
                        'numeric_mark' => 0,
                        'grade' => '',
                    ];
                }

                // Add NA for subject total as well with consistent styling
                $row[] = [
                    'subject_id' => $subject['id'],
                    'key' => "subject{$subject['id']}_total",
                    'type' => 'subject_total',
                    'label' => '-',
                    'class' => 'text-center',
                    'max_mark' => 0,
                    'numeric_mark' => 0,
                    'grade' => '',
                ];

                // Add NA for subject position as well
                $row[] = [
                    'subject_id' => $subject['id'],
                    'key' => "subject{$subject['id']}_position",
                    'type' => 'subject_position',
                    'label' => '-',
                    'class' => 'text-center',
                    'numeric_mark' => 0,
                ];

                continue;
            }

            // Track subject total for this student
            $studentSubjectTotal = 0;
            $studentSubjectMaxTotal = 0;
            $subjectMarks = [];

            foreach ($subject['assessments'] as $assessment) {
                $assessmentMark = $marks
                    ->where('subject_id', $subject['id'])
                    ->where('assessment_code', $assessment['code'])
                    ->first();

                $mark = collect($assessmentMark['marks'] ?? []);

                $maxMark = $assessment['max_mark'] ?? 0;

                $studentMark = $mark->firstWhere('uuid', $student->uuid);
                $obtainedMark = $studentMark['obtained_mark'] ?? '';

                $label = $studentMark['obtained_mark'] ?? '';

                if ($subject['has_grading']) {
                    $label = $this->getGrade($grade, $assessment['max_mark'], $obtainedMark);
                }

                $markClass = 'text-center';
                $subjectGrade = $this->getGrade($grade, $assessment['max_mark'], $obtainedMark);

                if (in_array($subjectGrade, $failGrades)) {
                    $markClass = 'text-center font-weight-bold';
                }

                if ($isElective) {
                    $label .= '*';
                }

                // Handle empty, null, or non-numeric values as 0, and display as '-' for consistency
                if ($label === '-') {
                    $numericMark = 0;
                    $label = '-';
                } else if (empty($obtainedMark) || $obtainedMark === '' || $obtainedMark === null || !is_numeric($obtainedMark)) {
                    $numericMark = 0;
                    $label = 0; // Change empty values to 0 for consistent display
                } else {
                    $numericMark = (float)$obtainedMark;
                }

                // Add to subject total
                $studentSubjectTotal += $numericMark;
                $studentSubjectMaxTotal += $maxMark;

                $markItem = [
                    'subject_id' => $subject['id'],
                    'assessment_code' => $assessment['code'],
                    'key' => "subject{$subject['id']}_{$assessment['code']}",
                    'type' => 'marks',
                    'label' => $label,
                    'class' => $markClass,
                    'text-style' => in_array($subjectGrade, $failGrades) ? 'circular-border' : '',
                    'max_mark' => $maxMark,
                    'numeric_mark' => $numericMark,
                    'grade' => $subjectGrade,
                ];

                $row[] = $markItem;
                $subjectMarks[] = $markItem;
            }

            // Calculate subject grade based on total
            $subjectGrade = $this->getGrade($grade, $studentSubjectMaxTotal, $studentSubjectTotal);

            // Add subject total column
            $markClass = 'text-center';

            if (in_array($subjectGrade, $failGrades)) {
                $markClass .= ' text-danger';
            }

            if ($isElective) {
                $studentSubjectTotal .= '*';
            }

            // For cumulative mark summary, we need to handle term totals, term positions,
            // aggregate totals, final percentages, and aggregate positions differently

            // This will be handled by the new structure - the existing getStudentMarks
            // method needs to be updated to handle the 3-row structure with terms and aggregates

            // For now, we'll add the basic subject total and position as placeholders
            // The actual implementation will be in the updated method

            $row[] = [
                'subject_id' => $subject['id'],
                'key' => "subject{$subject['id']}_total",
                'type' => 'subject_total',
                'label' => $studentSubjectTotal,
                'class' => $markClass,
                'text-style' => in_array($subjectGrade, $failGrades) ? 'circular-border' : '',
                'max_mark' => $studentSubjectMaxTotal,
                'numeric_mark' => is_numeric($studentSubjectTotal) ? $studentSubjectTotal : 0,
                'grade' => $subjectGrade,
            ];

            $row[] = [
                'subject_id' => $subject['id'],
                'key' => "subject{$subject['id']}_position",
                'type' => 'subject_position',
                'label' => '',
                'class' => 'text-center',
                'numeric_mark' => is_numeric($studentSubjectTotal) ? $studentSubjectTotal : 0,
            ];
        }

        return $row;
    }



    private function addSummaryRows(array &$rows, array $subjects, Collection $students, Collection $marks, Grade $grade, array $params)
    {
        // Add summary rows (same as MarkSummaryService)
        $mainSubjects = collect($subjects)->filter(function ($subject) {
            return !$subject['has_grading'];
        })->toArray();

        // Sort student rows by percentage in descending order (best to least)
        usort($rows, function($a, $b) {
            $percentA = collect($a)->firstWhere('key', 'percent')['label'] ?? 0;
            $percentB = collect($b)->firstWhere('key', 'percent')['label'] ?? 0;
            return $percentB <=> $percentA; // Sort in descending order
        });

        // Add summary rows (highest, lowest, average, etc.)
        // This would include the same summary logic as MarkSummaryService
        // For now, we'll skip the detailed implementation to keep it simple
    }

    private function getLayout(Request $request)
    {
        return [
            'watermark' => $request->boolean('showWatermark'),
            'margin_top' => 0,
            'box_width' => '100%',
        ];
    }

    private function getTitles(Request $request, Batch $batch, Collection $terms)
    {
        $titles = [];

        if ($request->filled('title')) {
            $titles[] = $request->title;
        } else {
            $titles[] = trans('exam.report.cumulative_mark_summary.cumulative_mark_summary');
        }

        $titles[] = ($batch->course?->name ?? 'Unknown Course') . ' - ' . ($batch->name ?? 'Unknown Batch');

        $termNames = $terms->pluck('name')->filter()->join(', ');
        if ($termNames) {
            $titles[] = trans('exam.term.term') . ': ' . $termNames;
        }

        //$titles[] = trans('exam.attempt.' . $request->attempt);

        // Add signatories
        $signatories = [];
        for ($i = 1; $i <= 4; $i++) {
            if ($request->filled("signatory_{$i}")) {
                $signatories[] = $request->input("signatory_{$i}");
            }
        }

        return compact('titles', 'signatories');
    }

    private function generatePdf(Request $request, array $titles, array $rows, array $header, array $termHeader, array $subHeader, array $layout, Batch $batch, Collection $terms)
    {
        $content = view('print.exam.report.cumulative-mark-summary', compact('titles', 'rows', 'header', 'termHeader', 'subHeader', 'layout', 'request'))->render();

        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => $request->paper_size ?? 'A4-L', // Default to landscape A4
            'margin_left' => 10,
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);
        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont = true;
        $mpdf->WriteHTML($content);

        // Set filename based on batch and terms
        $termNames = $terms->pluck('name')->filter()->join('-');
        $filename = Str::slug($batch->name . '-' . $termNames . '-cumulative-mark-summary') . '.pdf';

        // Output the PDF for download
        return $mpdf->Output($filename, 'D');
    }

    private function getCumulativeStudentMarks(Student $student, Grade $grade, Collection $marks, array $subjects, Collection $terms, array $row, array $params = [])
    {
        $subjectWiseStudents = Arr::get($params, 'subject_wise_students', collect([]));
        $failGrades = Arr::get($params, 'fail_grades', []);

        foreach ($subjects as $subject) {
            $notApplicableStudents = $subject['not_applicable_students'] ?? [];
            $subjectId = $subject['id'];

            $isElective = false;
            if (Arr::get($subject, 'is_elective')) {
                $isElective = $subjectWiseStudents
                    ->where('student_id', $student->id)
                    ->where('subject_id', $subject['id'])
                    ->first() ? true : false;
            }

            if (in_array($student->uuid, $notApplicableStudents)) {
                // Add NA for all assessments in this subject
                foreach ($subject['assessments'] as $assessment) {
                    $row[] = [
                        'subject_id' => $subject['id'],
                        'assessment_code' => $assessment['code'],
                        'key' => "subject{$subject['id']}_{$assessment['code']}",
                        'label' => '-',
                        'class' => 'text-center',
                        'max_mark' => 0,
                        'numeric_mark' => 0,
                        'grade' => '',
                        'type' => 'marks',
                    ];
                }
                continue;
            }

            // Process term-wise assessments + aggregate assessments
            $aggregateScores = [];

            // For each term: process assessments, calculate term total, add term position placeholder
            foreach ($terms as $term) {
                if (!isset($subject['terms'][$term->id])) continue;

                $termData = $subject['terms'][$term->id];
                $termTotal = 0;
                $termMaxTotal = 0;

                // Process each assessment in this term
                foreach ($termData['assessments'] as $assessment) {
                    $termCode = $term->name . '_' . $assessment['code'];

                    $assessmentMark = $marks
                        ->where('subject_id', $subject['id'])
                        ->where('assessment_code', $termCode)
                        ->first();

                    $mark = collect($assessmentMark['marks'] ?? []);
                    $maxMark = $assessment['max_mark'] ?? 0;
                    $studentMark = $mark->firstWhere('uuid', $student->uuid);
                    $obtainedMark = $studentMark['obtained_mark'] ?? '';

                    $label = $obtainedMark;
                    $numericMark = is_numeric($obtainedMark) ? (float)$obtainedMark : 0;

                    if ($isElective) {
                        $label .= '*';
                    }

                    $subjectGrade = $this->getGrade($grade, $maxMark, $numericMark);
                    $markClass = 'text-center';
                    if (in_array($subjectGrade, $failGrades)) {
                        $markClass = 'text-center font-weight-bold';
                    }

                    $row[] = [
                        'subject_id' => $subject['id'],
                        'assessment_code' => $termCode,
                        'key' => "subject{$subject['id']}_{$termCode}",
                        'type' => 'marks',
                        'label' => $label,
                        'class' => $markClass,
                        'text-style' => in_array($subjectGrade, $failGrades) ? 'circular-border' : '',
                        'max_mark' => $maxMark,
                        'numeric_mark' => $numericMark,
                        'grade' => $subjectGrade,
                    ];

                    $termTotal += $numericMark;
                    $termMaxTotal += $maxMark;

                    // Accumulate for aggregate (sum assessment scores across terms)
                    $assessmentCode = $assessment['code'];
                    if (!isset($aggregateScores[$assessmentCode])) {
                        $aggregateScores[$assessmentCode] = ['score' => 0, 'max_mark' => 0];
                    }
                    $aggregateScores[$assessmentCode]['score'] += $numericMark;
                    $aggregateScores[$assessmentCode]['max_mark'] += $maxMark;
                }

                // Add term total
                $termGrade = $this->getGrade($grade, $termMaxTotal, $termTotal);
                $row[] = [
                    'subject_id' => $subject['id'],
                    'key' => "subject{$subject['id']}_{$term->name}_total",
                    'type' => 'term_total',
                    'label' => $termTotal,
                    'class' => 'text-center',
                    'text-style' => in_array($termGrade, $failGrades) ? 'circular-border' : '',
                    'max_mark' => $termMaxTotal,
                    'numeric_mark' => $termTotal,
                    'grade' => $termGrade,
                ];

                // Add term position placeholder
                $row[] = [
                    'subject_id' => $subject['id'],
                    'key' => "subject{$subject['id']}_{$term->name}_position",
                    'type' => 'term_position',
                    'label' => '',
                    'class' => 'text-center',
                    'numeric_mark' => $termTotal,
                ];
            }

            // Calculate aggregate total (sum of all term totals for this subject)
            $aggregateTotal = 0;
            $aggregateMaxTotal = 0;
            foreach ($aggregateScores as $data) {
                $aggregateTotal += $data['score'];
                $aggregateMaxTotal += $data['max_mark'];
            }

            // Add aggregate total
            $aggregateGrade = $this->getGrade($grade, $aggregateMaxTotal, $aggregateTotal);
            $row[] = [
                'subject_id' => $subject['id'],
                'key' => "subject{$subject['id']}_aggregate_total",
                'type' => 'aggregate_total',
                'label' => $aggregateTotal,
                'class' => 'text-center',
                'text-style' => in_array($aggregateGrade, $failGrades) ? 'circular-border' : '',
                'max_mark' => $aggregateMaxTotal,
                'numeric_mark' => $aggregateTotal,
                'grade' => $aggregateGrade,
            ];

            // Add final percentage
            $finalPercent = $aggregateMaxTotal > 0 ? round(($aggregateTotal / $aggregateMaxTotal) * 100, 2) : 0;
            $row[] = [
                'subject_id' => $subject['id'],
                'key' => "subject{$subject['id']}_final_percent",
                'type' => 'final_percent',
                'label' => $finalPercent . '%',
                'class' => 'text-center',
                'numeric_mark' => $finalPercent,
            ];

            // Add aggregate position placeholder
            $row[] = [
                'subject_id' => $subject['id'],
                'key' => "subject{$subject['id']}_aggregate_position",
                'type' => 'aggregate_position',
                'label' => '',
                'class' => 'text-center',
                'numeric_mark' => $aggregateTotal,
            ];
        }

        return $row;
    }

    private function calculateStudentTermTotal(array $row, array $subjects, $term)
    {
        $termTotal = 0;

        foreach ($subjects as $subject) {
            $subjectId = $subject['id'];

            // Look for the term total for this subject
            $termTotalItem = collect($row)->firstWhere('key', "subject{$subjectId}_{$term->name}_total");

            if ($termTotalItem && isset($termTotalItem['numeric_mark']) && is_numeric($termTotalItem['numeric_mark'])) {
                $termTotal += $termTotalItem['numeric_mark'];
            }
        }

        return $termTotal;
    }

    /**
     * Filter subjects that have no actual data (all '-' or 'NA') based on report type
     * For term-wise: Filter subjects with no data for specific terms
     * For cumulative: Filter subjects with no data across ALL terms
     */
    private function filterSubjectsWithNoData(array $subjects, Collection $students, array $params): array
    {
        $filteredSubjects = [];

        foreach ($subjects as $subject) {
            $subjectId = $subject['id'];
            $hasAnyData = false;

            // Check if any student has actual numeric data for this subject
            foreach ($students as $student) {
                // Skip if student is not applicable for this subject
                $notApplicableStudents = $subject['not_applicable_students'] ?? [];
                if (in_array($student->uuid, $notApplicableStudents)) {
                    continue;
                }

                // Check if student has any numeric marks for this subject across all assessments
                foreach ($subject['assessments'] as $assessment) {
                    $assessmentMarks = collect($subject['marks'])->firstWhere('code', $assessment['code']);
                    if ($assessmentMarks) {
                        $studentMark = collect($assessmentMarks['marks'] ?? [])
                            ->firstWhere('uuid', $student->uuid);

                        $obtainedMark = $studentMark['obtained_mark'] ?? '';

                        // If we find any numeric mark, this subject has data
                        if (is_numeric($obtainedMark) && $obtainedMark !== '') {
                            $hasAnyData = true;
                            break 2; // Break out of both loops
                        }
                    }
                }
            }

            // Only include subjects that have actual data
            if ($hasAnyData) {
                $filteredSubjects[] = $subject;
            }
        }

        return $filteredSubjects;
    }
}
