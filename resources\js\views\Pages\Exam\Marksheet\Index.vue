<template>
    <PageHeader
        :title="$trans(route.meta.label)"
        :navs="[{ label: $trans('exam.exam'), path: 'Exam' }]"
    >
        <PageHeaderAction
            name="ExamMarksheet"
            :title="$trans('exam.marksheet.marksheet')"
            :actions="userActions"
            :dropdown-actions="dropdownActions"
            @toggleFilter="showFilter = !showFilter"
        >
            <BaseButton
                design="white"
                v-if="perform('exam-marksheet:access')"
                @click="router.push({ name: 'ExamMarksheetPrint' })"
                >{{
                    $trans("global.print", {
                        attribute: $trans("exam.marksheet.marksheet"),
                    })
                }}</BaseButton
            >

            <ResultDownloadButton
                v-if="perform('exam-marksheet:access') && hasValidFilters"
                result-type="marksheet"
                :download-url="'/api/v1/app/exam/marksheet/download-pdf'"
                :show-dropdown="true"
                :show-print="true"
                :show-download="true"
                page-type="legacy"
                :form-state="marksheetFilters"
                @print="onPrint"
                @download="onDownload"
                @error="onError"
            />
        </PageHeaderAction>
    </PageHeader>

    <ParentTransition appear :visibility="showFilter">
        <FilterForm
            :is-loading="isLoading"
            @afterFilter="fetchReport"
            :init-url="initUrl"
            :pre-requisites="preRequisites"
            :marksheet-filters="marksheetFilters"
            @hide="showFilter = false"
        ></FilterForm>
    </ParentTransition>

    <ParentTransition appear :visibility="true">
        <BaseCard no-padding no-content-padding :is-loading="isLoading">
        </BaseCard>
    </ParentTransition>
</template>

<script>
export default {
    name: "ExamMarksheet",
}
</script>

<script setup>
import { ref, reactive, computed, onMounted } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useStore } from "vuex"
import { perform } from "@core/helpers/action"
import FilterForm from "./Filter.vue"
import ResultDownloadButton from "@core/components/Ui/ResultDownloadButton.vue"
import { useMarksheetFilters } from "@core/composables/useMarksheetFilters"

const route = useRoute()
const router = useRouter()
const store = useStore()

// Initialize marksheet filters composable
const marksheetFilters = useMarksheetFilters('legacy')

let userActions = []

let dropdownActions = []

const initUrl = "exam/marksheet/"
const showFilter = ref(true)
const isLoading = ref(false)

const preRequisites = reactive({
    types: [],
    terms: [],
    exams: [],
})

// Use reactive validation from composable
const hasValidFilters = computed(() => {
    return marksheetFilters.validateForm.value
})

const preRequisite = async () => {
    isLoading.value = true
    await store
        .dispatch(initUrl + "preRequisite")
        .then((response) => {
            isLoading.value = false
            Object.assign(preRequisites, response)
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const fetchReport = async () => {
    isLoading.value = true
    await store
        .dispatch(initUrl + "fetchReport", {
            params: route.query,
        })
        .then((response) => {
            isLoading.value = false
            window.open("/print").document.write(response)
        })
        .catch((e) => {
            isLoading.value = false
        })
}

const onPrint = (data) => {
    console.log('Print initiated:', data)
}

const onDownload = (data) => {
    console.log('Download initiated:', data)
}

const onError = (error) => {
    console.error('Action failed:', error)
}

onMounted(async () => {
    await preRequisite()

    // await fetchReport()
})
</script>
