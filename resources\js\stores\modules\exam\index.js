import * as Api from "@core/apis"
import * as Form from "@core/utils/form"
import term from "@stores/modules/exam/term"
import grade from "@stores/modules/exam/grade"
import assessment from "@stores/modules/exam/assessment"
import observation from "@stores/modules/exam/observation"
import schedule from "@stores/modules/exam/schedule"
import form from "@stores/modules/exam/form"
import onlineExam from "@stores/modules/exam/onlineExam"
import questionBank from "@stores/modules/exam/questionBank"
import admitCard from "@stores/modules/exam/admitCard"
import mark from "@stores/modules/exam/mark"
import observationMark from "@stores/modules/exam/observationMark"
import comment from "@stores/modules/exam/comment"
import commentTemplate from "@stores/modules/exam/commentTemplate"
import attendance from "@stores/modules/exam/attendance"
import report from "@stores/modules/exam/report"
import marksheet from "@stores/modules/exam/marksheet"
import { useToast } from "vue-toastification"
import { mutations, actions, getters } from "@stores/global"

const toast = useToast()

const initialState = () => ({
    initURL: "/app/exams",
    formErrors: {},
})

const exam = {
    namespaced: true,
    state: initialState,
    modules: {
        term,
        grade,
        assessment,
        observation,
        schedule,
        form,
        onlineExam,
        questionBank,
        admitCard,
        mark,
        observationMark,
        comment,
        commentTemplate,
        attendance,
        report,
        marksheet,
    },
    mutations: {
        ...mutations,
    },
    actions: {
        ...actions,
        storeConfig({ state, commit }, payload) {
            return Api.custom({
                url: state.initURL + "/" + payload.uuid + "/config",
                method: "POST",
                data: payload.form,
            })
                .then((response) => {
                    toast.success(response.message)
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
    },
    getters: {
        ...getters,
    },
}
export default exam
