<?php

namespace App\Http\Controllers\Exam;

use App\Http\Controllers\Controller;
use App\Services\Exam\BulkResultDownloadService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class BulkDownloadController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:exam-marksheet:access')->only(['download', 'estimate']);
    }

    /**
     * Download bulk results as ZIP file
     */
    public function download(Request $request, BulkResultDownloadService $service)
    {
        $request->validate([
            'type' => 'required|string|in:marksheet,mark-summary,cumulative-mark-summary,exam-summary',
            'batches' => 'array|nullable',
            'batches.*' => 'integer|exists:batches,id',
            'students' => 'array|nullable',
            'students.*' => 'integer|exists:students,id',
            'paper_size' => 'string|nullable',
        ]);

        $options = [
            'batches' => $request->batches ?? [],
            'students' => $request->students ?? [],
        ];

        // Check if bulk download is allowed
        if (!$service->canPerformBulkDownload($request, $options)) {
            return response()->json([
                'message' => 'Bulk download request exceeds allowed limits',
                'errors' => ['bulk' => ['Too many files or estimated size too large']]
            ], 422);
        }

        try {
            $zipFilename = $service->generateBulkDownload($request, $options);
            
            // Return the ZIP file for download
            return response()->download($zipFilename, basename($zipFilename))->deleteFileAfterSend(true);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Bulk download failed',
                'errors' => ['bulk' => [$e->getMessage()]]
            ], 500);
        }
    }

    /**
     * Get estimated size and file count for bulk download
     */
    public function estimate(Request $request, BulkResultDownloadService $service)
    {
        $request->validate([
            'type' => 'required|string|in:marksheet,mark-summary,cumulative-mark-summary,exam-summary',
            'batches' => 'array|nullable',
            'batches.*' => 'integer|exists:batches,id',
            'students' => 'array|nullable',
            'students.*' => 'integer|exists:students,id',
        ]);

        $options = [
            'batches' => $request->batches ?? [],
            'students' => $request->students ?? [],
        ];

        $estimate = $service->getEstimatedSize($request, $options);
        $canDownload = $service->canPerformBulkDownload($request, $options);

        return response()->json([
            'estimate' => $estimate,
            'can_download' => $canDownload,
            'limits' => [
                'max_files' => config('exam.bulk_download.max_files', 50),
                'max_size_mb' => config('exam.bulk_download.max_size_mb', 100),
            ]
        ]);
    }
}
