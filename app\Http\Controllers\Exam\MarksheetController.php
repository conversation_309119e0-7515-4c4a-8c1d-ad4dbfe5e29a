<?php

namespace App\Http\Controllers\Exam;

use App\Http\Controllers\Controller;
use App\Services\Exam\MarksheetService;
use Illuminate\Http\Request;

class MarksheetController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:exam-marksheet:access')->only(['preRequisite', 'fetchReport', 'downloadPdf']);
    }

    public function preRequisite(Request $request, MarksheetService $service)
    {
        return response()->ok($service->preRequisite($request));
    }

    public function fetchReport(Request $request, MarksheetService $service)
    {
        return $service->fetchReport($request);
    }

    public function downloadPdf(Request $request, MarksheetService $service)
    {
        // Debug logging
        \Log::info('MarksheetController::downloadPdf called with MarksheetService', [
            'all_params' => $request->all(),
            'type' => $request->type,
            'batch' => $request->batch,
            'exam' => $request->exam,
            'term' => $request->term
        ]);

        // Set action to pdf to trigger PDF generation in MarksheetService
        $request->merge(['action' => 'pdf']);

        // Make sure paper_size is properly passed
        if ($request->has('paper_size')) {
            $request->merge(['paper_size' => $request->paper_size]);
        }

        \Log::info('MarksheetController::downloadPdf using working MarksheetService', [
            'action' => $request->action,
            'service' => 'MarksheetService (same as working print/filter)'
        ]);

        try {
            // Use the working MarksheetService (same as print/filter functionality)
            $result = $service->fetchReport($request);

            \Log::info('MarksheetService returned result', [
                'result_type' => gettype($result),
                'is_response' => $result instanceof \Illuminate\Http\Response,
                'content_length' => is_string($result) ? strlen($result) : 'N/A'
            ]);

            return $result;
        } catch (\Exception $e) {
            \Log::error('MarksheetController::downloadPdf failed with MarksheetService', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
}
