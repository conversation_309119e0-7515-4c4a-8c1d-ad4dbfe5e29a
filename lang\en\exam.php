<?php

return [
    'exam' => 'Exam',
    'exams' => 'Exams',
    'module_title' => 'List all Exams',
    'module_description' => 'Manage all Exams',
    'exam_report' => 'Exam Report',
    'incharge' => 'Exam Incharge',
    'total' => 'Total',
    'grand_total' => 'Grand Total',
    'total_short' => 'T',
    'percent' => 'Percent',
    'percentage' => 'Percentage',
    'percentage_short' => '%',
    'grade_short' => 'G',
    's_no' => 'SNo',
    'cgpa' => 'CGPA',
    'gpa' => 'AGPA',
    'result_date' => 'Date of Result',
    'scholastic_area' => 'Scholastic Area',
    'co_scholastic_area' => 'Co-Scholastic Area',
    'grading_subject' => 'Grading Subject',
    'result_grade' => 'Grade',
    'highest_mark' => 'Highest Mark',
    'average_mark' => 'Average Mark',
    'average_marks' => 'Avg.',
    'lowest_mark' => 'Lowest Mark',
    'total_absent' => 'Total Absent',
    'position' => 'P',
    'course_position' => 'Class Position',
    'highest' => 'C.H',
    'lowest' => 'C.L',
    'average' => 'C.A',
    //'legend' => 'Legend / Key',
    'legend' => 'Abbreviations',
    'abbreviations' => 'Abbreviations',
    'props' => [
        'name' => 'Name',
        'code' => 'Code',
        'display_name' => 'Display Name',
        'position' => 'Position',
        'course_position' => 'Class Position',
        'highest' => 'Highest',
        'lowest' => 'Lowest',
        'average' => 'Average',
        'description' => 'Description',
    ],
    'config' => [
        'config' => 'Config',
        'props' => [
            'marksheet_format' => 'Report Sheet Format',
            'visible_marksheet_types' => 'Visible Report Sheet Types',
            'show_in_report_sheet' => 'Show in Report Sheet',
            'comment_label' => 'Comment Label',
            'comment_behavioural_label' => 'Comment Behavioural Label',
            'show_class_average_mark' => 'Show Class Average Mark',
            'show_class_highest_mark' => 'Show Class Highest Mark',
            'show_class_lowest_mark' => 'Show Class Lowest Mark',
            'show_subject_position' => 'Show Subject Position',
            'show_course_position' => 'Show Course Position',
        ],
    ],
    'report_types' => [
        'mark_based' => 'Mark Based',
        'credit_based' => 'Credit Based',
    ],
    'result' => 'Result',
    'results' => [
        'pass' => 'Pass',
        'fail' => 'Fail',
        'reassessment' => 'Need Reassessment',
    ],
    'term' => [
        'term' => 'Exam Term',
        'terms' => 'Exam Terms',
        'module_title' => 'List all Exam Terms',
        'module_description' => 'Manage all Exam Terms',
        'props' => [
            'name' => 'Name',
            'display_name' => 'Display Name',
            'position' => 'Position',
            'description' => 'Description',
        ],
    ],
    'grade' => [
        'grade' => 'Exam Grade',
        'grades' => 'Exam Grades',
        'module_title' => 'List all Exam Grades',
        'module_description' => 'Manage all Exam Grades',
        'props' => [
            'name' => 'Name',
            'records' => 'Records',
            'code' => 'Code',
            'min_score' => 'Min Score',
            'max_score' => 'Max Score',
            'value' => 'Value',
            'point' => 'Grade Point',
            'credit_point' => 'Credit Point',
            'label' => 'Label',
            'fail_grade' => 'Fail Grade',
            'description' => 'Description',
        ],
    ],
    'online_exam' => [
        'online_exam' => 'Online Exam',
        'online_exams' => 'Online Exams',
        'preview' => 'Preview',
        'module_title' => 'List all Online Exams',
        'module_description' => 'Manage all Online Exams',
        'auto_publish_results' => 'Auto Publish Exam Result',
        'auto_publish_results_help' => 'Students see their results immediately after completion.',
        'already_published' => 'Online exam is already published.',
        'already_unpublished' => 'Online exam is not yet published.',
        'already_cancelled' => 'Online exam is already cancelled.',
        'already_submitted' => 'Online exam has already been submitted.',
        'continue_exam' => 'Continue Exam',
        'processing_results' => 'Processing Results',
        'results_pending' => 'Results Pending',
        'expired' => 'Expired',
        'could_not_update_status_if_date_time_passed_start_date_time' => 'Could not update status if current date and time is passed exam start date and time.',
        'could_not_publish_without_questions' => 'Could not publish online exam without questions.',
        'start_submission' => 'Start Submission',
        'finish_submission_info' => 'Please click on the button below to finish the submission. No changes can be made after submission.',
        'could_not_update_status_with_submissions' => 'Could not update status with submissions.',
        'already_result_published' => 'Online exam result is already published.',
        'could_not_publish_result_without_completing_exam' => 'Could not publish result without completing exam.',
        'negative_mark_not_allowed' => 'Negative marking is not allowed.',
        'evaluate' => 'Evaluate',
        'obtained_mark' => 'Obtained Mark',
        'comment' => 'Comment',
        'finish_submission' => 'Finish Submission',
        'current_time' => 'Current Time',
        'remaining_time' => ':attribute minutes remaining',
        'submit' => 'Submit',
        'progress' => 'Progress',
        'upcoming' => 'Upcoming',
        'starting_in' => 'Starting in :attribute minutes',
        'live' => 'Live',
        'completed' => 'Completed',
        'result' => 'Result',
        'preview_mode' => 'Preview Mode',
        'preview_info' => 'This is a preview of how students will see the exam. You can interact with the form elements to test the interface.',
        'preview_as_student' => 'Preview as Student',
        'end_date_info' => 'Leave blank if end date is same as date.',
        'not_available' => 'Exam is not available for taking',
        'time_expired' => 'Your exam time has expired',
        'flexible_timing' => 'Flexible Timing',
        'traditional_timing' => 'Traditional Timing',
        'duration_minutes' => 'Duration (Minutes)',
        'expiry_date' => 'Expiry Date',
        'expiry_time' => 'Expiry Time',
        'available' => 'Available',
        'expired' => 'Expired',
        'auto_submitted' => 'Auto Submitted',
        'flexible_timing_help' => 'Enable flexible timing to allow students to take the exam at any time within the specified duration',
        'duration_minutes_help' => 'Duration in minutes for the exam (1-480 minutes)',
        'expiry_date_help' => 'Optional: Set an expiry date after which the exam cannot be taken',
        'expiry_time_help' => 'Optional: Set an expiry time (defaults to 23:59:59)',
        'types' => [
            'mcq' => 'MCQ',
            'mixed' => 'Mixed',
        ],
        'question_types' => [
            'mcq' => 'MCQ',
            'single_line_question' => 'Single Line Question',
            'multi_line_question' => 'Multi Line Question',
            'file_upload' => 'File Upload',
        ],
        'props' => [
            'title' => 'Title',
            'start_datetime' => 'Start Date & Time',
            'date' => 'Date',
            'time' => 'Time',
            'start_time' => 'Start Time',
            'end_date' => 'End Date',
            'end_time' => 'End Time',
            'type' => 'Type',
            'pass_percentage' => 'Pass Percentage',
            'has_negative_marking' => 'Has Negative Marking',
            'negative_mark_percent_per_question' => 'Negative Mark Percent Per Question',
            'period' => 'Period',
            'duration' => 'Duration',
            'question' => 'Question',
            'answer' => 'Answer',
            'correct_answer' => 'Correct Answer',
            'options' => 'Options',
            'instructions' => 'Instructions',
            'description' => 'Description',
        ],
        'question' => [
            'question' => 'Question',
            'questions' => 'Questions',
            'module_title' => 'List all Online Exam Questions',
            'module_description' => 'Manage all Online Exam Questions',
            'auto_evaluate' => 'Auto Evaluate',
            'auto_evaluate_help' => 'Enable automatic evaluation for this question',
            'answer_placeholder' => 'Enter the correct answer for this question',
            'matching_options' => 'Answer Matching Options',
            'strict_match' => 'Strict Match',
            'strict_match_help' => 'Answer must match exactly',
            'case_sensitive' => 'Case Sensitive',
            'case_sensitive_help' => 'Consider letter case when matching',
            'ignore_spaces' => 'Ignore Spaces',
            'ignore_spaces_help' => 'Ignore spaces when matching answers',
            'props' => [
                'type' => 'Type',
                'title' => 'Question',
                'header' => 'Header',
                'option' => 'Option',
                'correct_answer' => 'Correct Answer',
                'mark' => 'Mark',
                'description' => 'Description',
                'answer' => 'Answer',
            ],
        ],
        'submission' => [
            'submission' => 'Submission',
            'submissions' => 'Submissions',
            'module_title' => 'List all Online Exam Submissions',
            'module_description' => 'Manage all Online Exam Submissions',
            'props' => [
                'started_at' => 'Started At',
                'submitted_at' => 'Submitted At',
                'evaluated_at' => 'Evaluated At',
                'obtained_mark' => 'Obtained Mark',
            ],
        ],
        'proctoring' => [
            'enabled_preview' => 'Proctoring is enabled for this exam',
        ],
    ],
    'question_bank' => [
        'question_bank' => 'Question Bank',
        'question_banks' => 'Question Banks',
        'module_title' => 'List all Question Banks',
        'module_description' => 'Manage all Question Banks',
        'add_option' => 'Add Option',
        'option_text' => 'Option Text',
        'correct_answer' => 'Correct Answer',
        'import_success' => 'Successfully imported :count out of :total questions.',
        'import_from_question_bank' => 'Import from Question Bank',
        'search_questions' => 'Search Questions',
        'select_questions' => 'Select Questions',
        'no_questions_found' => 'No questions found matching your criteria.',
        'intelligent_import' => 'Intelligent Import',
        'paste_questions' => 'Paste Questions',
        'paste_questions_placeholder' => 'Paste your questions here...',
        'paste_questions_help' => 'Paste your questions in the format shown in the example below. The system will automatically detect question types and extract options.',
        'example_format' => 'Example Format',
        'parse_questions' => 'Parse Questions',
        'preview_questions' => 'Preview Questions',
        'questions_parsed' => 'questions parsed',
        'parsing_errors' => 'parsing errors',
        'parsing_errors_found' => 'Parsing Errors Found',
        'question' => 'Question',
        'create_questions' => 'Create Questions',
        'bulk_create_success' => 'Successfully created :count out of :total questions.',
        'correct' => 'Correct',
        'props' => [
            'subject' => 'Subject',
            'class' => 'Class',
            'batch' => 'Batch',
            'batches' => 'Batches',
            'type' => 'Type',
            'title' => 'Question',
            'header' => 'Header',
            'description' => 'Description',
            'mark' => 'Mark',
            'options' => 'Options',
            'answer' => 'Answer',
        ],
        'validation' => [
            'mcq_requires_correct_answer' => 'At least one option must be marked as correct for MCQ questions.',
            'mcq_requires_options' => 'At least two options are required for MCQ questions.','mcq_only_one_correct_answer' => 'Only one option can be marked as correct for MCQ questions.',
        ],
        'auto_evaluate' => 'Auto Evaluate',
        'auto_evaluate_help' => 'Enable automatic evaluation for this question',
        'answer_placeholder' => 'Enter the correct answer for this question',
        'matching_options' => 'Answer Matching Options',
        'strict_match' => 'Strict Match',
        'strict_match_help' => 'Answer must match exactly',
        'case_sensitive' => 'Case Sensitive',
        'case_sensitive_help' => 'Consider letter case when matching',
        'ignore_spaces' => 'Ignore Spaces',
        'ignore_spaces_help' => 'Ignore spaces when matching answers',
        'import_info' => 'Upload a CSV or Excel file to import multiple questions at once. All question types (MCQ, Single Line, Multi Line) can be imported in the same file. For the class field, use format "Course Name-Batch Name" (e.g., "Grade 10-Section A").',
        'import_format' => 'File Format Requirements',
        'import_format_description' => 'Your file must be in CSV or Excel format with the following column structure:',
        'required_columns' => 'Required Columns',
        'optional_columns' => 'Optional Columns',
        'mcq_columns' => 'MCQ Question Columns',
        'single_line_columns' => 'Single Line Question Columns',
        'question_types' => 'Supported Question Types',
        'column_subject_help' => 'Subject name (must exist in system)',
        'column_type_help' => 'Question type: mcq, single_line_question, or multi_line_question',
        'column_title_help' => 'The question text',
        'column_mark_help' => 'Points awarded for correct answer (positive number)',
        'column_header_help' => 'Optional header text for the question',
        'column_description_help' => 'Optional description or additional context',
        'column_class_help' => 'Optional class/grade level',
        'column_options_help' => 'Option text for MCQ questions (option_1 to option_6)',
        'column_correct_help' => 'Mark as correct answer (true/false)',
        'column_answer_help' => 'Correct answer for single line questions',
        'column_auto_evaluate_help' => 'Enable auto evaluation (true/false)',
        'column_strict_match_help' => 'Require exact match (true/false)',
        'column_case_sensitive_help' => 'Case sensitive matching (true/false)',
        'column_ignore_spaces_help' => 'Ignore spaces in answers (true/false)',
        'type_mcq_help' => 'Multiple choice questions with predefined options',
        'type_single_line_help' => 'Short answer questions with optional auto-evaluation',
        'type_multi_line_help' => 'Essay or long answer questions',
        'sample_data' => 'Sample Data Format',
        'import_notes' => 'Important Notes',
        'import_note_1' => 'MCQ questions must have at least one option and exactly one correct answer',
        'import_note_2' => 'Single line questions with answers will be auto-evaluated if auto_evaluate is true',
        'import_note_3' => 'Boolean fields accept: true/false, 1/0, yes/no, y/n',
        'import_note_4' => 'Maximum 500 questions can be imported at once',
        'errors' => [
            'cannot_delete_used_question' => 'Cannot delete question that is used in online exams.',
            'no_questions_selected' => 'Please select at least one question to import.',
        ],
        'bulk_delete_success' => 'Successfully deleted :deleted out of :total question banks. :skipped were skipped.',
        'bulk_delete_warning' => 'Are you sure you want to delete :count selected question banks?',
        'bulk_delete_all_warning' => 'Are you sure you want to delete all question banks matching the current filter?',
        'bulk_delete_note' => 'Question deleted cannot be recovered.',
    ],
    'assessment' => [
        'assessment' => 'Exam Assessment',
        'assessments' => 'Exam Assessments',
        'module_title' => 'List all Exam Assessments',
        'module_description' => 'Manage all Exam Assessments',
        'could_not_change_code_after_mark_recorded' => 'Could not change code after mark recorded.',
        'props' => [
            'name' => 'Name',
            'type' => 'Assessment Type',
            'code' => 'Code',
            'mark' => 'Mark',
            'marks' => 'Marks',
            'max_mark' => 'Max Mark',
            'passing_mark' => 'Passing Mark',
            'description' => 'Description',
        ],
    ],
    'observation' => [
        'observation' => 'Observation Parameter',
        'observations' => 'Observation Parameters',
        'module_title' => 'List all Observation Parameters',
        'module_description' => 'Manage all Observation Parameters',
        'props' => [
            'name' => 'Name',
            'parameter' => 'Observation Parameter',
            'code' => 'Code',
            'max_mark' => 'Max Mark',
            'description' => 'Description',
        ],
    ],
    'schedule' => [
        'schedule' => 'Exam Timetable',
        'schedules' => 'Exam Timetables',
        'module_title' => 'List all Exam Timetables',
        'module_description' => 'Manage all Exam Timetables',
        'could_not_find_record' => 'Could not find exam for this subject.',
        'record_has_no_exam' => 'This subject is marked as no exam subject.',
        'copy_to_course' => 'Copy to Class',
        'could_not_find_result' => 'Could not find result for this exam.',
        'form' => 'Exam Form',
        'reassessment' => 'Reassessment',
        'marksheet_statuses' => [
            'processed' => 'Processed',
            'pending' => 'Pending',
        ],
        'marksheet_processed_info' => 'Report Sheet has been processed. Any changes in marks will means reprocessing the report sheet.',
        'attempt_number' => ':attribute Attempt',
        'attempts' => [
            'first' => 'First',
            'second' => 'Second',
            'third' => 'Third',
            'fourth' => 'Fourth',
            'fifth' => 'Fifth',
        ],
        'props' => [
            'date' => 'Date',
            'period' => 'Period',
            'max_mark' => 'Max Mark',
            'marks' => 'Marks',
            'applicable' => 'Applicable',
            'start_time' => 'Start Time',
            'duration' => 'Duration',
            'end_time' => 'End Time',
            'not_applicable' => 'Not Applicable',
            'grading' => 'Grading',
            'attempt' => 'Attempt',
            'has_grading' => 'Has Grading',
            'description' => 'Description',
        ],
    ],
    'form' => [
        'form' => 'Exam Form',
        'module_title' => 'List all Exam Forms',
        'module_description' => 'Manage all Exam Forms',
        'not_eligible_to_submit' => 'You are not eligible to submit exam form.',
        'fee_balance' => 'Could not submit form due to fee balance of :attribute.',
        'expired_info' => 'This form cannot be submitted as it is expired.',
        'last_date_expired' => 'Could not submit form due to last date expired.',
        'confirmed' => 'Form has been confirmed and waiting for submission.',
        'submitted' => 'Form has been submitted and waiting for approval.',
        'already_submitted' => 'Form has already been submitted.',
        'confirmed_info' => 'Form has been confirmed at :datetime and waiting for submission.',
        'submitted_info' => 'Form has been submitted at :datetime and waiting for approval.',
        'approved_info' => 'Form has been approved at :datetime.',
        'fee_info' => 'Total exam fee payable is :attribute.',
        'fee_confirmation' => 'I have checked all the subjects and fee and confirm that they are correct.',
        'disclaimer' => 'I have checked all the details and confirm that they are correct. Any changes after submission will not be entertained.',
        'props' => [
            'fee' => 'Exam Form Fee',
            'late_fee' => 'Late Fee',
            'last_date' => 'Last Date',
            'submitted_at' => 'Submitted At',
            'approved_at' => 'Approved At',
        ],
    ],
    'mark' => 'Exam Mark',
    'observation_mark' => 'Observation Mark',
    'max_mark' => 'Max Mark',
    'max_mark_short' => 'MM',
    'total_marks' => 'Total Marks',
    'obtained_marks' => 'Obtained Marks',
    'obtained_mark' => 'Obtained Mark',
    'obtained_mark_short' => 'OB',
    'comment' => 'Comment - Academics',
    'subject_comment' => 'Subject Teacher\'s Comment',
    //'result' => 'Comment - Behavioural',
    'comment_behavioural' => 'Comment - Behavioural',
    'comment_template' => [
        'comment_template' => 'Comment Template',
        'comment_templates' => 'Comment Templates',
        'add_comment_template' => 'Add Comment Template',
        'edit_comment_template' => 'Edit Comment Template',
        'apply_templates' => 'Apply Templates',
        'select_template' => 'Select Template',
        'comment_type' => 'Comment Type',
        'apply_to_selected' => 'Apply to Selected',
        'apply_to_all' => 'Apply to All',
        'template_help_text' => 'Select students using checkboxes, choose a template, and click apply to automatically fill comments.',
        'applied_successfully' => 'Template applied successfully to :count students.',
        'course_rules' => 'Course-based Rules',
        'course_rules_description' => 'Define specific templates for different courses or course patterns.',
        'rule' => 'Rule',
        'add_rule' => 'Add Rule',
        'rule_template' => 'Rule Template',
        'rule_template_placeholder' => 'Template content for this rule (e.g., "promoted to {next_course_name}")',
        'course_name_pattern_placeholder' => 'e.g., "year 1" or "grade 5"',
        'available_placeholders' => 'Available Placeholders',
        'academic_content_placeholder' => 'e.g., "promoted to {next_course_name}"',
        'behavioral_content_placeholder' => 'e.g., "Excellent behavior and discipline"',
        'types' => [
            'academic' => 'Academic',
            'behavioral' => 'Behavioral',
        ],
        'placeholders' => [
            'course_name' => 'Current course name',
            'division_name' => 'Division name',
            'current_year' => 'Current year',
            'next_course_name' => 'Next course name (for academic templates)',
        ],
        'props' => [
            'name' => 'Template Name',
            'content' => 'Template Content',
            'type' => 'Template Type',
            'is_active' => 'Is Active',
            'description' => 'Description',
            'course_name_pattern' => 'Course Name Pattern',
            'specific_course' => 'Specific Course',
            'rule_template' => 'Rule Template',
        ],
    ],
    'record' => 'Record Mark',
    'could_not_record_observation_mark_without_observation_parameter' => 'Could not find observation parameter for this exam.',
    'marks_record_permission_denied' => 'You do not have permission to record marks for this exam.',
    'marksheet' => [
        'type' => 'Type',
        'cumulative' => 'Cumulative',
        'term_wise' => 'Term Wise',
        'exam_wise' => 'Exam Wise',
        'total_credit' => 'Total Credit',
        'obtained_credit' => 'Obtained Credit',
        'exam_wise_credit_based' => 'Exam Wise (Credit Based)',
        'exam_wise_default' => 'Exam Wise (Default)',
        'marksheet' => 'Report Sheet',
        'published' => 'Report Sheet Published',
        'processed' => 'Report Sheet Processed',
        'template' => 'Template',
        'cumulative_assessment' => 'Cumulative Assessment',
        'not_generated' => 'Report Sheet is not yet generated.',
        'not_published' => 'Report Sheet is not yet published.',
        'course_wise' => 'Class Wise',
        'show_summary_report' => 'Show Summary Report',
        'sort_summary_report_by_rank' => 'Sort Summary Report by Rank',
    ],
    'admit_card' => [
        'admit_card' => 'Admit Card',
    ],
    'report' => [
        'report' => 'Report',
        'reports' => 'Reports',
        'course_wise' => 'Class Wise',
        'mark_summary' => [
            'mark_summary' => 'Broadsheet',
            'module_title' => 'View Broadsheet',
            'module_description' => 'Summarize subject wise marks for all students.',
        ],
        'cumulative_mark_summary' => [
            'cumulative_mark_summary' => 'Cumulative Broadsheet',
            'module_title' => 'View Cumulative Broadsheet',
            'module_description' => 'Summarize subject wise marks for all students across all terms and exams.',
            'all_terms' => 'All Terms',
            'all_subjects' => 'All Subjects',
            'default_title' => 'Cumulative Mark Summary Report',
        ],
        'exam_summary' => [
            'exam_summary' => 'Exam Summary Report',
            'module_title' => 'Get Exam Summary Report',
            'module_description' => 'Summary report for all students.',
        ],
    ],
];
