import term from "./term"
import grade from "./grade"
import assessment from "./assessment"
import observation from "./observation"
import schedule from "./schedule"
import form from "./form"
import onlineExam from "./onlineExam"
import questionBank from "./questionBank"
import commentTemplate from "./commentTemplate"
import report from "./report"

export default [
    {
        path: "exam",
        name: "ExamIndex",
        redirect: { name: "ExamList" },
        meta: {
            moduleName: "exam",
            label: "exam.exam",
            icon: "fas fa-newspaper",
            permissions: [
                "exam:manage",
                "exam-term:manage",
                "exam-grade:manage",
                "exam-assessment:manage",
                "exam-observation:manage",
                "exam-schedule:read",
                "exam-form:manage",
                "online-exam:read",
                "question-bank:read",
                "exam:marks-record",
                "exam:subject-incharge-wise-marks-record",
                "exam:course-incharge-wise-marks-record",
                "exam:report",
                "exam-admit-card:access",
                "exam-marksheet:access",
                "exam-comment-template:manage",
            ],
        },
        component: {
            template: "<router-view></router-view>",
        },
        children: [
            {
                path: "config",
                name: "ExamConfig",
                redirect: { name: "ExamConfigGeneral" },
                meta: {
                    isNotNav: true,
                    label: "exam.exam",
                    icon: "fas fa-cog",
                    hideChildren: true,
                    permissions: ["exam:config"],
                },
                component: () => import("@views/Pages/Exam/Config/Index.vue"),
                children: [
                    {
                        path: "general",
                        name: "ExamConfigGeneral",
                        meta: {
                            label: "exam.config.config",
                            icon: "far fa-building",
                            key: "general",
                        },
                        component: () =>
                            import("@views/Pages/Exam/Config/General.vue"),
                    },
                    {
                        path: "proctoring",
                        name: "ExamConfigProctoring",
                        meta: {
                            label: "exam.proctoring.config.title",
                            icon: "fas fa-shield-alt",
                            key: "proctoring",
                            permissions: ["exam:config", "online-exam:manage"],
                        },
                        component: () =>
                            import("@views/Pages/Exam/Config/Proctoring.vue"),
                    },
                ],
            },
            {
                path: "exams",
                name: "ExamLists",
                redirect: { name: "ExamList" },
                meta: {
                    label: "exam.exams",
                    icon: "fas fa-right-long",
                    permissions: ["exam:manage"],
                },
            },
            ...term,
            ...grade,
            ...assessment,
            ...observation,
            ...schedule,
            ...form,
            ...onlineExam,
            ...questionBank,
            ...commentTemplate,
            {
                path: "admit-card",
                name: "ExamAdmitCard",
                meta: {
                    label: "exam.admit_card.admit_card",
                    icon: "fas fa-right-long",
                    permissions: ["exam-admit-card:access"],
                },
                component: () =>
                    import("@views/Pages/Exam/AdmitCard/Index.vue"),
            },
            {
                path: "mark",
                name: "ExamMark",
                meta: {
                    label: "exam.mark",
                    icon: "fas fa-right-long",
                    permissions: [
                        "exam:marks-record",
                        "exam:subject-incharge-wise-marks-record",
                        "exam:course-incharge-wise-marks-record",
                    ],
                },
                component: () => import("@views/Pages/Exam/Mark/Index.vue"),
            },
            {
                path: "observation-mark",
                name: "ExamObservationMark",
                meta: {
                    isNotNav: true,
                    label: "exam.observation_mark",
                    icon: "fas fa-right-long",
                    permissions: [
                        "exam:marks-record",
                        "exam:subject-incharge-wise-marks-record",
                        "exam:course-incharge-wise-marks-record",
                    ],
                },
                component: () =>
                    import("@views/Pages/Exam/ObservationMark/Index.vue"),
            },
            {
                path: "comment",
                name: "ExamComment",
                meta: {
                    isNotNav: true,
                    label: "exam.comment",
                    icon: "fas fa-right-long",
                    permissions: [
                        "exam:marks-record",
                        "exam:subject-incharge-wise-marks-record",
                        "exam:course-incharge-wise-marks-record",
                    ],
                },
                component: () => import("@views/Pages/Exam/Comment/Index.vue"),
            },
            {
                path: "attendance",
                name: "ExamAttendance",
                meta: {
                    isNotNav: true,
                    label: "student.attendance.attendance",
                    icon: "fas fa-right-long",
                    permissions: [
                        "exam:marks-record",
                        "exam:subject-incharge-wise-marks-record",
                        "exam:course-incharge-wise-marks-record",
                    ],
                },
                component: () =>
                    import("@views/Pages/Exam/Attendance/Index.vue"),
            },
            {
                path: "marksheet",
                name: "ExamMarksheet",
                redirect: { name: "ExamMarksheetLegacy" },
                meta: {
                    label: "exam.marksheet.marksheet",
                    icon: "fas fa-right-long",
                    permissions: ["exam-marksheet:access"],
                },
                component: () =>
                    import("@views/Pages/Exam/Marksheet/Index.vue"),
            },
            {
                path: "marksheet/legacy",
                name: "ExamMarksheetLegacy",
                meta: {
                    isNotNav: true,
                    label: "exam.marksheet.marksheet",
                    icon: "fas fa-right-long",
                    permissions: ["exam-marksheet:access"],
                },
                component: () =>
                    import("@views/Pages/Exam/Marksheet/Index.vue"),
            },
            {
                path: "marksheet/process",
                name: "ExamMarksheetProcess",
                meta: {
                    isNotNav: true,
                    label: "exam.marksheet.marksheet",
                    icon: "fas fa-right-long",
                    permissions: ["exam-marksheet:process"],
                },
                component: () =>
                    import("@views/Pages/Exam/Marksheet/Process/Index.vue"),
            },
            {
                path: "marksheet/print",
                name: "ExamMarksheetPrint",
                meta: {
                    isNotNav: true,
                    label: "exam.marksheet.marksheet",
                    icon: "fas fa-right-long",
                    permissions: ["exam-marksheet:access"],
                },
                component: () =>
                    import("@views/Pages/Exam/Marksheet/Print/Index.vue"),
            },
            ...report,
        ],
    },
    {
        path: "exams",
        name: "Exam",
        redirect: { name: "ExamList" },
        meta: {
            isNotNav: true,
            label: "exam.exam",
            icon: "fas fa-user-graduate",
            hideChildren: true,
            permissions: ["exam:manage"],
        },
        component: {
            template: "<router-view></router-view>",
        },
        children: [
            {
                path: "",
                name: "ExamList",
                meta: {
                    trans: "global.list",
                    label: "exam.exams",
                },
                component: () => import("@views/Pages/Exam/Index.vue"),
            },
            {
                path: "create",
                name: "ExamCreate",
                meta: {
                    type: "create",
                    action: "create",
                    trans: "global.add",
                    label: "exam.exam",
                },
                component: () => import("@views/Pages/Exam/Action.vue"),
            },
            {
                path: ":uuid/edit",
                name: "ExamEdit",
                meta: {
                    type: "edit",
                    action: "update",
                    trans: "global.edit",
                    label: "exam.exam",
                },
                component: () => import("@views/Pages/Exam/Action.vue"),
            },
            {
                path: ":uuid/duplicate",
                name: "ExamDuplicate",
                meta: {
                    type: "duplicate",
                    action: "create",
                    trans: "global.duplicate",
                    label: "exam.exam",
                },
                component: () => import("@views/Pages/Exam/Action.vue"),
            },
            {
                path: ":uuid",
                name: "ExamShow",
                meta: {
                    trans: "global.detail",
                    label: "exam.exam",
                },
                component: () => import("@views/Pages/Exam/Show.vue"),
            },
        ],
    },
]
