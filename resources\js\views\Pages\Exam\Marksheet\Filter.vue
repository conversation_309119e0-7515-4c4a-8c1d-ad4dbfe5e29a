<template>
    <FilterForm
        :init-form="initForm"
        :multiple="['students']"
        :form="form"
        @hide="emit('hide')"
    >
        <div class="grid grid-cols-3 gap-6" v-if="fetchData.isLoaded">
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-model="form.type"
                    name="type"
                    :label="$trans('exam.marksheet.type')"
                    :options="preRequisites.types"
                    v-model:error="formErrors.type"
                />
            </div>
            <div
                class="col-span-3 sm:col-span-1"
                v-if="form.type == 'term_wise'"
            >
                <BaseSelect
                    v-model="form.term"
                    name="term"
                    :label="$trans('exam.term.term')"
                    :options="preRequisites.terms"
                    value-prop="uuid"
                    v-model:error="formErrors.term"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.name }}
                        <span class="ml-1"
                            >({{
                                slotProps.value.division?.name ||
                                $trans("general.all")
                            }})</span
                        >
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.name }}
                        <span class="ml-1"
                            >({{
                                slotProps.option.division?.name ||
                                $trans("general.all")
                            }})</span
                        >
                    </template>
                </BaseSelect>
            </div>
            <div
                class="col-span-3 sm:col-span-1"
                v-if="
                    form.type == 'exam_wise' ||
                    form.type == 'exam_wise_default' ||
                    form.type == 'exam_wise_credit_based'
                "
            >
                <BaseSelect
                    v-model="form.exam"
                    name="exam"
                    :label="$trans('exam.exam')"
                    value-prop="uuid"
                    :options="preRequisites.exams"
                    v-model:error="formErrors.exam"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.name }}
                        <span class="ml-1" v-if="slotProps.value.term"
                            >({{
                                slotProps.value.term?.division?.name ||
                                $trans("general.all")
                            }})</span
                        >
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.name }}
                        <span class="ml-1" v-if="slotProps.option.term"
                            >({{
                                slotProps.option.term?.division?.name ||
                                $trans("general.all")
                            }})</span
                        >
                    </template>
                </BaseSelect>
            </div>
            <div class="col-span-3 sm:col-span-1">
                <BaseSelectSearch
                    v-if="fetchData.isLoaded"
                    name="batch"
                    :label="
                        $trans('global.select', {
                            attribute: $trans('academic.batch.batch'),
                        })
                    "
                    v-model="form.batch"
                    v-model:error="formErrors.batch"
                    value-prop="uuid"
                    :init-search="fetchData.batch"
                    search-key="course_batch"
                    search-action="academic/batch/list"
                    @selected="onBatchSelect"
                    @removed="onBatchRemove"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.course.name }}
                        {{ slotProps.value.name }}
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.course.nameWithTerm }}
                        {{ slotProps.option.name }}
                    </template>
                </BaseSelectSearch>
            </div>
            <!-- <div class="col-span-3 sm:col-span-1">
                <BaseSelectSearch
                    v-if="fetchData.isLoaded && form.batch"
                    multiple
                    name="students"
                    :label="
                        $trans('global.select', {
                            attribute: $trans('student.student'),
                        })
                    "
                    v-model="form.students"
                    v-model:error="formErrors.students"
                    value-prop="uuid"
                    :init-search="fetchData.students"
                    search-key="name"
                    search-action="student/summary"
                    :additional-search-query="{ batches: [form.batch] }"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.name }} ({{
                            slotProps.value.courseName +
                            " " +
                            slotProps.value.batchName
                        }})
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.name }} ({{
                            slotProps.option.courseName +
                            " " +
                            slotProps.option.batchName
                        }})
                    </template>
                </BaseSelectSearch>
            </div> -->
            <div class="col-span-3 sm:col-span-1">
                <BaseSelect
                    v-if="form.batch"
                    multiple
                    name="students"
                    :label="
                        $trans('global.select', {
                            attribute: $trans('student.student'),
                        })
                    "
                    :options="state.students"
                    v-model="form.students"
                    v-model:error="formErrors.students"
                    track-by="name"
                    value-prop="uuid"
                >
                    <template #selectedOption="slotProps">
                        {{ slotProps.value.name }} ({{
                            slotProps.value.courseName +
                            " " +
                            slotProps.value.batchName
                        }})
                    </template>

                    <template #listOption="slotProps">
                        {{ slotProps.option.name }} ({{
                            slotProps.option.courseName +
                            " " +
                            slotProps.option.batchName
                        }})
                    </template>
                </BaseSelect>
            </div>
        </div>

        <BaseFieldset
            class="mt-4"
            v-if="!actingAs(['student', 'guardian'], 'any')"
        >
            <template #legend>
                <div class="flex items-center gap-2">
                    {{
                        $trans("global.show", {
                            attribute: $trans("general.options"),
                        })
                    }}
                    <BaseSwitch
                        reverse
                        v-model="showOptions"
                        name="showOptions"
                    />
                </div>
            </template>
            <template v-if="showOptions">
                <div class="grid grid-cols-3 gap-6">
                    <div
                        class="col-span-3 sm:col-span-1"
                        v-if="
                            form.type == 'exam_wise' ||
                            form.type == 'exam_wise_default'
                        "
                    >
                        <BaseSwitch
                            vertical
                            v-model="form.cumulativeAssessment"
                            name="showSno"
                            :label="
                                $trans('exam.marksheet.cumulative_assessment')
                            "
                            v-model:error="formErrors.cumulativeAssessment"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <DatePicker
                            v-model="form.resultDate"
                            name="resultDate"
                            :label="$trans('exam.result_date')"
                            no-clear
                            v-model:error="formErrors.resultDate"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <BaseInput
                            type="number"
                            v-model="form.column"
                            name="column"
                            :label="$trans('print.column')"
                            v-model:error="formErrors.column"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <BaseInput
                            :leading-text="$trans('list.unit.mm')"
                            type="number"
                            v-model="form.marginTop"
                            name="marginTop"
                            :label="$trans('print.margin_top')"
                            v-model:error="formErrors.marginTop"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <BaseSwitch
                            vertical
                            v-model="form.showCourseWise"
                            name="showCourseWise"
                            :label="$trans('exam.marksheet.course_wise')"
                            v-model:error="formErrors.showCourseWise"
                        />
                    </div>
                    <div class="col-span-3 sm:col-span-1">
                        <BaseSwitch
                            vertical
                            v-model="form.showAllStudent"
                            name="showAllStudent"
                            :label="
                                $trans('global.list_all', {
                                    attribute: $trans('student.student'),
                                })
                            "
                            v-model:error="formErrors.showAllStudent"
                        />
                    </div>
                    <template v-if="form.students.length == 0">
                        <div class="col-span-3 sm:col-span-1">
                            <BaseSwitch
                                vertical
                                v-model="form.showSummaryReport"
                                name="showSummaryReport"
                                :label="
                                    $trans('exam.marksheet.show_summary_report')
                                "
                                v-model:error="formErrors.showSummaryReport"
                            />
                        </div>
                        <div
                            class="col-span-3 sm:col-span-1"
                            v-if="form.showSummaryReport"
                        >
                            <BaseSwitch
                                vertical
                                v-model="form.sortSummaryReportByRank"
                                name="sortSummaryReportByRank"
                                :label="
                                    $trans(
                                        'exam.marksheet.sort_summary_report_by_rank'
                                    )
                                "
                                v-model:error="
                                    formErrors.sortSummaryReportByRank
                                "
                            />
                        </div>
                    </template>
                </div>
            </template>
        </BaseFieldset>
    </FilterForm>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue"
import { useRoute } from "vue-router"
import { useStore } from "vuex"
import { toBoolean } from "@core/helpers/string"
import { actingAs, getFormErrors } from "@core/helpers/action"

const route = useRoute()
const store = useStore()

const emit = defineEmits(["hide"])

const props = defineProps({
    initUrl: {
        type: String,
        default: "",
    },
    preRequisites: {
        type: Object,
        default() {
            return {}
        },
    },
    marksheetFilters: {
        type: Object,
        default: null,
    },
})

const initForm = {
    type: "",
    term: "",
    exam: "",
    batch: "",
    students: [],
    cumulativeAssessment: false,
    resultDate: "",
    column: 1,
    marginTop: 0,
    showCourseWise: false,
    showAllStudent: false,
    showSummaryReport: false,
    sortSummaryReportByRank: false,
}

const isLoading = ref(false)
const showOptions = ref(false)
const formErrors = getFormErrors(props.initUrl)

// Use form from composable if provided, otherwise use local form
const form = props.marksheetFilters ? props.marksheetFilters.form : reactive({ ...initForm })

const state = reactive({
    exams: props.preRequisites.exams,
    students: [],
})

const fetchData = reactive({
    exam: "",
    batch: "",
    students: [],
    isLoaded:
        route.query.exam || route.query.batch || route.query.students
            ? false
            : true,
})

const cancel = () => {
    emit("cancel")
}

const onBatchSelect = async (batch) => {
    state.students = []
    form.students = []

    let status = "studying"

    if (form.showAllStudent) {
        status = "all"
    }

    isLoading.value = true
    await store
        .dispatch("student/listAll", {
            params: {
                batch: batch.uuid,
                status: status,
            },
        })
        .then((response) => {
            state.students = response
            isLoading.value = false
        })
        .catch((e) => {
            isLoading.value = false
        })

    fetchData.students = []
}

const onBatchRemove = () => {
    state.students = []
    form.students = []
    fetchData.students = []
}

onMounted(async () => {
    if (_.isEmpty(route.query)) {
        fetchData.isLoaded = true
        return
    }

    form.type = route.query.type
    fetchData.exam = route.query.exam
    form.exam = route.query.exam
    fetchData.batch = route.query.batch
    form.batch = route.query.batch

    if (route.query.batch) {
        await onBatchSelect({ uuid: route.query.batch })
        form.students = route.query.students
            ? route.query.students.split(",")
            : []
    }

    form.showCourseWise = toBoolean(route.query.showCourseWise || "")
    form.showAllStudent = toBoolean(route.query.showAllStudent || "")
    form.showSummaryReport = toBoolean(route.query.showSummaryReport || "")
    form.sortSummaryReportByRank = toBoolean(
        route.query.sortSummaryReportByRank || ""
    )
    form.cumulativeAssessment = toBoolean(
        route.query.cumulativeAssessment || ""
    )

    fetchData.isLoaded = true
})
</script>
