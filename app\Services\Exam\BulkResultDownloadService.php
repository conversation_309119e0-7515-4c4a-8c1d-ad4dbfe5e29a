<?php

namespace App\Services\Exam;

use App\Models\Academic\Batch;
use App\Models\Exam\Exam;
use App\Models\Exam\Term;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use ZipArchive;

class BulkResultDownloadService
{
    protected $resultPdfService;

    public function __construct(ResultPdfService $resultPdfService)
    {
        $this->resultPdfService = $resultPdfService;
    }

    /**
     * Generate bulk PDF downloads for multiple students/batches
     */
    public function generateBulkDownload(Request $request, array $options = [])
    {
        $type = $request->type;
        $batches = $options['batches'] ?? [];
        $students = $options['students'] ?? [];
        
        if (empty($batches) && empty($students)) {
            throw new \InvalidArgumentException('Either batches or students must be provided for bulk download');
        }

        $tempDir = storage_path('app/temp/bulk-downloads/' . Str::random(10));
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $files = [];
        
        try {
            // Generate PDFs for each batch or student
            if (!empty($batches)) {
                $files = $this->generateBatchPdfs($request, $batches, $tempDir);
            } elseif (!empty($students)) {
                $files = $this->generateStudentPdfs($request, $students, $tempDir);
            }

            // Create ZIP file
            $zipFilename = $this->createZipFile($files, $tempDir, $type);
            
            return $zipFilename;
        } finally {
            // Clean up temporary files
            $this->cleanupTempFiles($tempDir);
        }
    }

    /**
     * Generate PDFs for multiple batches
     */
    private function generateBatchPdfs(Request $request, array $batches, string $tempDir): array
    {
        $files = [];
        
        foreach ($batches as $batchId) {
            $batch = Batch::find($batchId);
            if (!$batch) continue;

            // Create a new request for this batch
            $batchRequest = clone $request;
            $batchRequest->merge(['batch' => $batchId]);

            // Generate the PDF content
            $filename = Str::slug($batch->name . '-' . $request->type) . '.pdf';
            $filepath = $tempDir . '/' . $filename;

            // Use the existing PDF generation logic
            $pdfContent = $this->generateSinglePdf($batchRequest);
            file_put_contents($filepath, $pdfContent);

            $files[] = [
                'path' => $filepath,
                'name' => $filename
            ];
        }

        return $files;
    }

    /**
     * Generate PDFs for individual students
     */
    private function generateStudentPdfs(Request $request, array $students, string $tempDir): array
    {
        $files = [];
        
        foreach ($students as $studentId) {
            // Create a new request for this student
            $studentRequest = clone $request;
            $studentRequest->merge(['student' => $studentId]);

            // Generate the PDF content
            $filename = Str::slug('student-' . $studentId . '-' . $request->type) . '.pdf';
            $filepath = $tempDir . '/' . $filename;

            // Use the existing PDF generation logic
            $pdfContent = $this->generateSinglePdf($studentRequest);
            file_put_contents($filepath, $pdfContent);

            $files[] = [
                'path' => $filepath,
                'name' => $filename
            ];
        }

        return $files;
    }

    /**
     * Generate a single PDF using the existing service
     */
    private function generateSinglePdf(Request $request): string
    {
        // This would call the appropriate service method based on the type
        switch ($request->type) {
            case 'marksheet':
                // You would need to implement this method in ResultPdfService
                return $this->resultPdfService->generateMarksheetPdfContent($request);
            case 'mark-summary':
                // You would need to implement this method in ResultPdfService
                return $this->resultPdfService->generateMarkSummaryPdfContent($request);
            default:
                throw new \InvalidArgumentException('Unsupported PDF type: ' . $request->type);
        }
    }

    /**
     * Create ZIP file from generated PDFs
     */
    private function createZipFile(array $files, string $tempDir, string $type): string
    {
        $zipFilename = $tempDir . '/bulk-' . $type . '-' . date('Y-m-d-H-i-s') . '.zip';
        
        $zip = new ZipArchive();
        if ($zip->open($zipFilename, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('Cannot create ZIP file');
        }

        foreach ($files as $file) {
            $zip->addFile($file['path'], $file['name']);
        }

        $zip->close();

        return $zipFilename;
    }

    /**
     * Clean up temporary files
     */
    private function cleanupTempFiles(string $tempDir): void
    {
        if (file_exists($tempDir)) {
            $files = glob($tempDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            rmdir($tempDir);
        }
    }

    /**
     * Get estimated file size for bulk download
     */
    public function getEstimatedSize(Request $request, array $options = []): array
    {
        $batches = $options['batches'] ?? [];
        $students = $options['students'] ?? [];
        
        $estimatedPdfSize = 500; // KB per PDF (rough estimate)
        $totalFiles = count($batches) + count($students);
        $estimatedTotalSize = $totalFiles * $estimatedPdfSize;

        return [
            'total_files' => $totalFiles,
            'estimated_size_kb' => $estimatedTotalSize,
            'estimated_size_mb' => round($estimatedTotalSize / 1024, 2),
        ];
    }

    /**
     * Check if bulk download is allowed based on limits
     */
    public function canPerformBulkDownload(Request $request, array $options = []): bool
    {
        $maxFiles = config('exam.bulk_download.max_files', 50);
        $maxSizeMb = config('exam.bulk_download.max_size_mb', 100);
        
        $estimate = $this->getEstimatedSize($request, $options);
        
        return $estimate['total_files'] <= $maxFiles && 
               $estimate['estimated_size_mb'] <= $maxSizeMb;
    }
}
