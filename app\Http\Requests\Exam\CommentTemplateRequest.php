<?php

namespace App\Http\Requests\Exam;

use App\Models\Exam\CommentTemplate;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CommentTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:100',
                Rule::unique('comment_templates', 'name')->ignore($this->route('comment_template'), 'uuid'),
            ],
            'content' => 'required|string|min:2|max:1000',
            'type' => 'required|in:academic,behavioral',
            'is_active' => 'boolean',
            'description' => 'nullable|string|max:500',
            'course_rules' => 'nullable|array',
            'course_rules.progression_rules' => 'nullable|array',
            'course_rules.progression_rules.*.template' => 'required_with:course_rules.progression_rules|string|max:1000',
            'course_rules.progression_rules.*.course_name_pattern' => 'nullable|string|max:100',
            'course_rules.progression_rules.*.course_uuid' => 'nullable|uuid',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.unique' => trans('validation.unique', ['attribute' => trans('exam.comment_template.props.name')]),
            'content.required' => trans('validation.required', ['attribute' => trans('exam.comment_template.props.content')]),
            'type.required' => trans('validation.required', ['attribute' => trans('exam.comment_template.props.type')]),
            'type.in' => trans('validation.in', ['attribute' => trans('exam.comment_template.props.type')]),
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => trans('exam.comment_template.props.name'),
            'content' => trans('exam.comment_template.props.content'),
            'type' => trans('exam.comment_template.props.type'),
            'is_active' => trans('exam.comment_template.props.is_active'),
            'description' => trans('exam.comment_template.props.description'),
        ];
    }
}
