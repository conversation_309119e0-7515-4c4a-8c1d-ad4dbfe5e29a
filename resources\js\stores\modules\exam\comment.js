import * as Api from "@core/apis"
import * as Form from "@core/utils/form"
import { useToast } from "vue-toastification"
import { toQueryString } from "@core/helpers/array"
import { mutations, actions, getters } from "@stores/global"

const toast = useToast()

const initialState = () => ({
    initURL: "/app/exam/comment",
    formErrors: {},
})

const comment = {
    namespaced: true,
    state: initialState,
    modules: {},
    mutations: {
        ...mutations,
    },
    actions: {
        resetFormErrors: actions.resetFormErrors,
        preRequisite({ state, commit }, payload) {
            return Api.custom({
                url: state.initURL + "/pre-requisite",
                method: "GET",
            })
                .then((response) => {
                    return response
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
        fetch({ state, commit }, payload) {
            let url = state.initURL + "/fetch"
            return Api.custom({
                url: toQueryString(url, payload.params),
                method: "GET",
            })
                .then((response) => {
                    return response
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
        store({ state, commit }, payload) {
            return Api.custom({
                url: state.initURL,
                method: "POST",
                data: payload.form,
            })
                .then((response) => {
                    toast.success(response.message)
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
        remove({ state, commit }, payload) {
            return Api.custom({
                url: state.initURL,
                method: "DELETE",
                data: payload.form,
            })
                .then((response) => {
                    toast.success(response.message)
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
        applyTemplate({ state, commit }, payload) {
            return Api.custom({
                url: state.initURL + "/apply-template",
                method: "POST",
                data: payload.form,
            })
                .then((response) => {
                    toast.success(response.message)
                    return response
                })
                .catch((error) => {
                    commit("SET_FORM_ERRORS", Form.getErrors(error))
                    throw error
                })
        },
    },
    getters: {
        ...getters,
    },
}

export default comment
