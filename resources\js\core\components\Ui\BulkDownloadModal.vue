<template>
    <BaseModal :show="show" @close="$emit('close')" size="lg">
        <template #title>
            <i class="fas fa-download mr-2"></i>
            {{ $trans('general.bulk_download') }}
        </template>

        <div class="space-y-6">
            <!-- Selection Type -->
            <div>
                <BaseLabel>{{ $trans('general.download_type') }}</BaseLabel>
                <div class="mt-2 space-y-2">
                    <BaseRadio
                        v-model="selectionType"
                        value="batches"
                        :label="$trans('general.by_batches')"
                    />
                    <BaseRadio
                        v-model="selectionType"
                        value="students"
                        :label="$trans('general.by_students')"
                    />
                </div>
            </div>

            <!-- Batch Selection -->
            <div v-if="selectionType === 'batches'">
                <BaseLabel>{{ $trans('general.select_batches') }}</BaseLabel>
                <BaseSelect
                    multiple
                    v-model="selectedBatches"
                    :options="batchOptions"
                    :placeholder="$trans('general.select_batches')"
                />
            </div>

            <!-- Student Selection -->
            <div v-if="selectionType === 'students'">
                <BaseLabel>{{ $trans('general.select_students') }}</BaseLabel>
                <BaseSelect
                    multiple
                    v-model="selectedStudents"
                    :options="studentOptions"
                    :placeholder="$trans('general.select_students')"
                />
            </div>

            <!-- Paper Size -->
            <div>
                <BaseLabel>{{ $trans('general.paper_size') }}</BaseLabel>
                <BaseSelect v-model="paperSize" :options="paperSizeOptions" />
            </div>

            <!-- Estimate Display -->
            <div v-if="estimate" class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">{{ $trans('general.download_estimate') }}</h4>
                <div class="space-y-1 text-sm text-gray-600">
                    <div>{{ $trans('general.total_files') }}: {{ estimate.total_files }}</div>
                    <div>{{ $trans('general.estimated_size') }}: {{ estimate.estimated_size_mb }} MB</div>
                    <div v-if="!estimate.can_download" class="text-red-600 font-medium">
                        {{ $trans('general.exceeds_limits') }}
                    </div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div v-if="isDownloading" class="space-y-2">
                <div class="flex justify-between text-sm">
                    <span>{{ $trans('general.preparing_download') }}</span>
                    <span>{{ downloadProgress }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div 
                        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        :style="{ width: downloadProgress + '%' }"
                    ></div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-between">
                <BaseButton design="white" @click="$emit('close')">
                    {{ $trans('general.cancel') }}
                </BaseButton>
                <div class="space-x-2">
                    <BaseButton 
                        design="white" 
                        @click="getEstimate"
                        :is-loading="isEstimating"
                    >
                        {{ $trans('general.get_estimate') }}
                    </BaseButton>
                    <BaseButton 
                        design="primary" 
                        @click="startDownload"
                        :is-loading="isDownloading"
                        :disabled="!canDownload"
                    >
                        {{ $trans('general.download') }}
                    </BaseButton>
                </div>
            </div>
        </template>
    </BaseModal>
</template>

<script>
export default {
    name: "BulkDownloadModal",
}
</script>

<script setup>
import { ref, computed, watch } from "vue"
import { useRoute } from "vue-router"
import axios from "axios"

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    resultType: {
        type: String,
        required: true,
    },
    batchOptions: {
        type: Array,
        default: () => [],
    },
    studentOptions: {
        type: Array,
        default: () => [],
    },
})

const emit = defineEmits(['close', 'download', 'error'])

const route = useRoute()

const selectionType = ref('batches')
const selectedBatches = ref([])
const selectedStudents = ref([])
const paperSize = ref('A4-L')
const estimate = ref(null)
const isEstimating = ref(false)
const isDownloading = ref(false)
const downloadProgress = ref(0)

const paperSizeOptions = [
    { value: 'A4-P', label: 'A4 Portrait' },
    { value: 'A4-L', label: 'A4 Landscape' },
    { value: 'A3-P', label: 'A3 Portrait' },
    { value: 'A3-L', label: 'A3 Landscape' },
    { value: 'Letter-P', label: 'Letter Portrait' },
    { value: 'Letter-L', label: 'Letter Landscape' },
    { value: 'Legal-P', label: 'Legal Portrait' },
    { value: 'Legal-L', label: 'Legal Landscape' },
]

const canDownload = computed(() => {
    const hasSelection = selectionType.value === 'batches' 
        ? selectedBatches.value.length > 0 
        : selectedStudents.value.length > 0
    
    return hasSelection && estimate.value?.can_download && !isDownloading.value
})

// Reset estimate when selection changes
watch([selectionType, selectedBatches, selectedStudents], () => {
    estimate.value = null
})

const getEstimate = async () => {
    isEstimating.value = true
    
    try {
        const data = {
            type: props.resultType,
            paper_size: paperSize.value,
            ...route.query,
        }

        if (selectionType.value === 'batches') {
            data.batches = selectedBatches.value
        } else {
            data.students = selectedStudents.value
        }

        const response = await axios.post('/app/exam/reports/bulk-download/estimate', data)
        estimate.value = response.data
    } catch (error) {
        console.error('Failed to get estimate:', error)
        emit('error', error)
    } finally {
        isEstimating.value = false
    }
}

const startDownload = async () => {
    isDownloading.value = true
    downloadProgress.value = 0
    
    try {
        const data = {
            type: props.resultType,
            paper_size: paperSize.value,
            ...route.query,
        }

        if (selectionType.value === 'batches') {
            data.batches = selectedBatches.value
        } else {
            data.students = selectedStudents.value
        }

        // Simulate progress
        const progressInterval = setInterval(() => {
            if (downloadProgress.value < 90) {
                downloadProgress.value += Math.random() * 10
            }
        }, 500)

        const response = await axios.post('/app/exam/reports/bulk-download', data, {
            responseType: 'blob'
        })

        clearInterval(progressInterval)
        downloadProgress.value = 100

        // Create download link
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `bulk-${props.resultType}-${new Date().toISOString().split('T')[0]}.zip`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)

        emit('download', { type: 'bulk', data })
        emit('close')
    } catch (error) {
        console.error('Bulk download failed:', error)
        emit('error', error)
    } finally {
        isDownloading.value = false
        downloadProgress.value = 0
    }
}
</script>
