<template>
    <FormAction
        :pre-requisites="true"
        @setPreRequisites="setPreRequisites"
        :init-url="initUrl"
        :init-form="initForm"
        :form="form"
        :setForm="setForm"
        redirect="ExamCommentTemplate"
    >
        <div class="grid grid-cols-2 gap-6">
            <div class="col-span-2 sm:col-span-1">
                <BaseInput
                    type="text"
                    v-model="form.name"
                    name="name"
                    :label="$trans('exam.comment_template.props.name')"
                    v-model:error="formErrors.name"
                    autofocus
                />
            </div>
            <div class="col-span-2 sm:col-span-1">
                <BaseSelect
                    v-model="form.type"
                    name="type"
                    :label="$trans('exam.comment_template.props.type')"
                    :options="preRequisites.types"
                    label-prop="label"
                    value-prop="value"
                    v-model:error="formErrors.type"
                />
            </div>
            <div class="col-span-2">
                <BaseTextarea
                    v-model="form.content"
                    name="content"
                    :label="$trans('exam.comment_template.props.content')"
                    :placeholder="getContentPlaceholder()"
                    v-model:error="formErrors.content"
                    :rows="4"
                />
                <div class="mt-2 text-sm text-gray-500">
                    <p>{{ $trans('exam.comment_template.available_placeholders') }}:</p>
                    <ul class="list-disc list-inside mt-1">
                        <li><code>{course_name}</code> - {{ $trans('exam.comment_template.placeholders.course_name') }}</li>
                        <li><code>{division_name}</code> - {{ $trans('exam.comment_template.placeholders.division_name') }}</li>
                        <li><code>{current_year}</code> - {{ $trans('exam.comment_template.placeholders.current_year') }}</li>
                        <li v-if="form.type === 'academic'"><code>{next_course_name}</code> - {{ $trans('exam.comment_template.placeholders.next_course_name') }}</li>
                    </ul>
                </div>
            </div>
            <div class="col-span-2">
                <BaseTextarea
                    v-model="form.description"
                    name="description"
                    :label="$trans('exam.comment_template.props.description')"
                    v-model:error="formErrors.description"
                    :rows="2"
                />
            </div>
            <div class="col-span-2 sm:col-span-1">
                <BaseCheckbox
                    v-model="form.isActive"
                    name="isActive"
                    :label="$trans('exam.comment_template.props.is_active')"
                />
            </div>
        </div>

        <!-- Academic Template Course Rules -->
        <div v-if="form.type === 'academic'" class="mt-6">
            <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                {{ $trans('exam.comment_template.course_rules') }}
            </h4>
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    {{ $trans('exam.comment_template.course_rules_description') }}
                </p>
                
                <div v-for="(rule, index) in form.courseRules.progressionRules" :key="index" class="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded">
                    <div class="flex justify-between items-center mb-2">
                        <h5 class="font-medium">{{ $trans('exam.comment_template.rule') }} {{ index + 1 }}</h5>
                        <BaseButton
                            design="danger"
                            size="sm"
                            @click="removeRule(index)"
                            v-if="form.courseRules.progressionRules.length > 1"
                        >
                            <i class="fas fa-trash"></i>
                        </BaseButton>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <BaseInput
                                type="text"
                                v-model="rule.courseNamePattern"
                                :name="`courseRules.progressionRules.${index}.courseNamePattern`"
                                :label="$trans('exam.comment_template.props.course_name_pattern')"
                                :placeholder="$trans('exam.comment_template.course_name_pattern_placeholder')"
                            />
                        </div>
                        <div>
                            <BaseSelect
                                v-model="rule.courseUuid"
                                :name="`courseRules.progressionRules.${index}.courseUuid`"
                                :label="$trans('exam.comment_template.props.specific_course')"
                                :options="preRequisites.courses"
                                label-prop="name"
                                value-prop="uuid"
                                :placeholder="$trans('global.select', { attribute: $trans('academic.course.course') })"
                            />
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <BaseTextarea
                            v-model="rule.template"
                            :name="`courseRules.progressionRules.${index}.template`"
                            :label="$trans('exam.comment_template.props.rule_template')"
                            :placeholder="$trans('exam.comment_template.rule_template_placeholder')"
                            :rows="3"
                        />
                    </div>
                </div>
                
                <BaseButton
                    design="secondary"
                    size="sm"
                    @click="addRule"
                >
                    <i class="fas fa-plus mr-2"></i>
                    {{ $trans('exam.comment_template.add_rule') }}
                </BaseButton>
            </div>
        </div>
    </FormAction>
</template>

<script>
export default {
    name: "ExamCommentTemplateForm",
    data() {
        return {
            initUrl: "exam/commentTemplate/",
            preRequisites: {
                types: [],
                courses: [],
            },
            form: {
                name: "",
                content: "",
                type: "behavioral",
                description: "",
                isActive: true,
                courseRules: {
                    progressionRules: [
                        {
                            courseNamePattern: "",
                            courseUuid: "",
                            template: "",
                        },
                    ],
                },
            },
            formErrors: {},
        }
    },
    computed: {
        initForm() {
            return {
                name: "",
                content: "",
                type: "behavioral",
                description: "",
                isActive: true,
                courseRules: {
                    progressionRules: [
                        {
                            courseNamePattern: "",
                            courseUuid: "",
                            template: "",
                        },
                    ],
                },
            }
        },
    },
    methods: {
        setPreRequisites(data) {
            this.preRequisites = data
        },
        setForm(data) {
            // The axios interceptor automatically converts snake_case to camelCase
            // So the data is already in camelCase format
            this.form = {
                ...this.form,
                ...data,
                // Handle any specific conversions if needed
                courseRules: data.courseRules || {
                    progressionRules: [
                        {
                            courseNamePattern: "",
                            courseUuid: "",
                            template: "",
                        },
                    ],
                },
            }
        },
        getContentPlaceholder() {
            if (this.form.type === 'academic') {
                return this.$trans('exam.comment_template.academic_content_placeholder')
            }
            return this.$trans('exam.comment_template.behavioral_content_placeholder')
        },
        addRule() {
            this.form.courseRules.progressionRules.push({
                courseNamePattern: "",
                courseUuid: "",
                template: "",
            })
        },
        removeRule(index) {
            this.form.courseRules.progressionRules.splice(index, 1)
        },

    },
    watch: {
        'form.type'(newType) {
            if (newType !== 'academic') {
                this.form.courseRules = {
                    progressionRules: [],
                }
            } else if (this.form.courseRules.progressionRules.length === 0) {
                this.form.courseRules.progressionRules = [
                    {
                        courseNamePattern: "",
                        courseUuid: "",
                        template: "",
                    },
                ]
            }
        },
    },
}
</script>
