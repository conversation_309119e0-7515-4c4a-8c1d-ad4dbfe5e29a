<template>
    <div class="flex items-center space-x-2">
        <!-- Print Button -->
        <BaseButton
            v-if="showPrint"
            design="white"
            size="sm"
            :is-loading="isPrintLoading"
            @click="handlePrint"
            v-tooltip="$trans('general.print')"
        >
            <i class="fas fa-print mr-2"></i>
            {{ $trans('general.print') }}
        </BaseButton>

        <!-- PDF Download Button -->
        <BaseButton
            v-if="showDownload"
            design="primary"
            size="sm"
            :is-loading="isDownloadLoading"
            @click="handleDownload"
            v-tooltip="$trans('general.download_pdf')"
        >
            <i class="fas fa-file-pdf mr-2"></i>
            {{ $trans('general.download_pdf') }}
        </BaseButton>

        <!-- Combined Dropdown Button -->
        <DropdownButton
            v-if="showDropdown"
            :label="$trans('general.download')"
            as="button"
        >
            <DropdownItem @click="handlePrint">
                <i class="fas fa-print mr-2"></i>
                {{ $trans('general.print') }}
            </DropdownItem>
            <DropdownItem @click="handleDownload">
                <i class="fas fa-file-pdf mr-2"></i>
                {{ $trans('general.download_pdf') }}
            </DropdownItem>
            <DropdownItem v-if="showPaperSizeOption" @click="showPaperSizeModal = true">
                <i class="fas fa-cog mr-2"></i>
                {{ $trans('general.options') }}
            </DropdownItem>
            <DropdownItem v-if="showBulkDownload" @click="showBulkDownloadModal = true">
                <i class="fas fa-archive mr-2"></i>
                {{ $trans('general.bulk_download') }}
            </DropdownItem>
        </DropdownButton>
    </div>

    <!-- Paper Size Selection Modal -->
    <BaseModal :show="showPaperSizeModal" @close="showPaperSizeModal = false">
        <template #title>
            {{ $trans('general.download_options') }}
        </template>

        <div class="space-y-4">
            <div>
                <BaseLabel>{{ $trans('general.paper_size') }}</BaseLabel>
                <BaseSelect v-model="selectedPaperSize" :options="paperSizeOptions" />
            </div>
        </div>

        <template #footer>
            <div class="flex justify-end space-x-2">
                <BaseButton design="white" @click="showPaperSizeModal = false">
                    {{ $trans('general.cancel') }}
                </BaseButton>
                <BaseButton design="primary" @click="handleDownloadWithOptions">
                    {{ $trans('general.download') }}
                </BaseButton>
            </div>
        </template>
    </BaseModal>

    <!-- Bulk Download Modal -->
    <BulkDownloadModal
        :show="showBulkDownloadModal"
        :result-type="resultType"
        :batch-options="bulkDownloadOptions.batches"
        :student-options="bulkDownloadOptions.students"
        @close="showBulkDownloadModal = false"
        @download="onBulkDownload"
        @error="(error) => emit('error', error)"
    />
</template>

<script>
export default {
    name: "ResultDownloadButton",
}
</script>

<script setup>
import { ref } from "vue"
import { useRoute } from "vue-router"
import { useStore } from "vuex"
import DropdownButton from "@core/components/Ui/DropdownButton.vue"
import DropdownItem from "@core/components/Ui/DropdownItem.vue"
import BulkDownloadModal from "@core/components/Ui/BulkDownloadModal.vue"

const props = defineProps({
    // Type of result: 'marksheet', 'mark-summary', 'cumulative-mark-summary', 'exam-summary'
    resultType: {
        type: String,
        required: true,
    },
    // Download URL for PDF
    downloadUrl: {
        type: String,
        required: true,
    },
    // Print URL (optional, if different from download)
    printUrl: {
        type: String,
        default: null,
    },
    // Show print button
    showPrint: {
        type: Boolean,
        default: true,
    },
    // Show download button
    showDownload: {
        type: Boolean,
        default: true,
    },
    // Show as dropdown instead of separate buttons
    showDropdown: {
        type: Boolean,
        default: false,
    },
    // Show paper size option
    showPaperSizeOption: {
        type: Boolean,
        default: true,
    },
    // Additional query parameters
    queryParams: {
        type: Object,
        default: () => ({}),
    },
    // Custom filename for download
    filename: {
        type: String,
        default: null,
    },
    // Show success notification
    showNotifications: {
        type: Boolean,
        default: true,
    },
    // Show bulk download option
    showBulkDownload: {
        type: Boolean,
        default: false,
    },
    // Options for bulk download (batches and students)
    bulkDownloadOptions: {
        type: Object,
        default: () => ({
            batches: [],
            students: [],
        }),
    },
    // Page type for validation (legacy, print, regular)
    pageType: {
        type: String,
        default: 'legacy',
    },
    // Form state for reactive validation
    formState: {
        type: Object,
        default: null,
    },
})

const emit = defineEmits(['print', 'download', 'error'])

const route = useRoute()
const store = useStore()

const isPrintLoading = ref(false)
const isDownloadLoading = ref(false)
const showPaperSizeModal = ref(false)
const showBulkDownloadModal = ref(false)
const selectedPaperSize = ref('A4-L')

const paperSizeOptions = [
    { value: 'A4-P', label: 'A4 Portrait' },
    { value: 'A4-L', label: 'A4 Landscape' },
    { value: 'A3-P', label: 'A3 Portrait' },
    { value: 'A3-L', label: 'A3 Landscape' },
    { value: 'Letter-P', label: 'Letter Portrait' },
    { value: 'Letter-L', label: 'Letter Landscape' },
    { value: 'Legal-P', label: 'Legal Portrait' },
    { value: 'Legal-L', label: 'Legal Landscape' },
]

const handlePrint = async () => {
    isPrintLoading.value = true

    try {
        const url = props.printUrl || props.downloadUrl.replace('/download-pdf', '')
        // Use form state if provided, otherwise fall back to route.query
        const baseParams = props.formState ? props.formState.getQueryParams() : route.query
        const queryParams = { ...baseParams, ...props.queryParams }
        const queryString = new URLSearchParams(queryParams).toString()
        const fullUrl = `${url}?${queryString}`

        // Dispatch to store for print action
        await store.dispatch(getStoreAction('fetchReport'), {
            params: queryParams,
        }).then((response) => {
            window.open("/print").document.write(response)
            emit('print', { url: fullUrl, params: queryParams })
        })
    } catch (error) {
        console.error('Print failed:', error)
        emit('error', error)
    } finally {
        isPrintLoading.value = false
    }
}

const handleDownload = async () => {
    await downloadPdf()
}

const handleDownloadWithOptions = async () => {
    showPaperSizeModal.value = false
    await downloadPdf(selectedPaperSize.value)
}

const downloadPdf = async (paperSize = 'A4-L') => {
    isDownloadLoading.value = true

    try {
        // Use form state if provided, otherwise fall back to route.query
        const baseParams = props.formState ? props.formState.getQueryParams() : route.query
        const queryParams = {
            ...baseParams,
            ...props.queryParams,
            paper_size: paperSize
        }

        const queryString = new URLSearchParams(queryParams).toString()
        const fullUrl = `${props.downloadUrl}?${queryString}`

        // Debug logging
        console.log('Download URL:', fullUrl)
        console.log('Query Params:', queryParams)

        // Use fetch for better error handling and proper PDF download
        const response = await fetch(fullUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        // Check if response is actually a PDF
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/pdf')) {
            // Clone response to read text for debugging
            const responseClone = response.clone()
            const responseText = await responseClone.text()
            console.error('Expected PDF but got:', {
                contentType,
                status: response.status,
                statusText: response.statusText,
                responseText: responseText.substring(0, 500) // First 500 chars
            })
            throw new Error(`Server did not return a PDF file. Got: ${contentType}`)
        }

        // Get the blob and create download
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)

        const link = document.createElement('a')
        link.href = url
        const downloadFilename = props.filename || `${props.resultType}-${new Date().toISOString().split('T')[0]}.pdf`
        link.setAttribute('download', downloadFilename)

        document.body.appendChild(link)
        link.click()

        // Clean up
        setTimeout(() => {
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
        }, 100)

        emit('download', { url: fullUrl, params: queryParams })

        // Show success notification if enabled
        if (props.showNotifications) {
            console.log('Download started successfully')
        }
    } catch (error) {
        console.error('Download failed:', error)
        emit('error', error)

        // Show error notification if enabled
        if (props.showNotifications) {
            console.error('Download failed. Please try again.')
        }
    } finally {
        isDownloadLoading.value = false
    }
}

const onBulkDownload = (data) => {
    console.log('Bulk download initiated:', data)
    emit('download', data)
}

const getStoreAction = (action) => {
    const actionMap = {
        'marksheet': 'exam/marksheet/',
        'mark-summary': 'exam/report/',
        'cumulative-mark-summary': 'exam/report/',
        'exam-summary': 'exam/report/',
    }
    
    return (actionMap[props.resultType] || 'exam/marksheet/') + action
}
</script>
