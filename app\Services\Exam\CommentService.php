<?php

namespace App\Services\Exam;

use App\Actions\Student\FetchBatchWiseStudent;
use App\Http\Resources\Exam\ExamResource;
use App\Http\Resources\Student\StudentResource;
use App\Models\Academic\Batch;
use App\Models\Exam\Exam;
use App\Models\Exam\Schedule;
use App\Models\Exam\CommentTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class CommentService
{
    public function preRequisite(Request $request)
    {
        $exams = ExamResource::collection(Exam::query()
            ->with('term.division')
            ->byPeriod()
            ->get());

        $commentTemplates = [
            'academic' => CommentTemplate::active()->academic()->orderBy('name')->get(),
            'behavioral' => CommentTemplate::active()->behavioral()->orderBy('name')->get(),
        ];

        return compact('exams', 'commentTemplates');
    }

    private function validateInput(Request $request): array
    {
        $request->validate([
            'exam' => 'required|uuid',
            'batch' => 'required|uuid',
        ]);

        $exam = Exam::query()
            ->with('term.division')
            ->byPeriod()
            ->whereUuid($request->exam)
            ->getOrFail(trans('exam.exam'), 'exam');

        $batch = Batch::query()
            ->byPeriod()
            ->filterAccessible()
            ->whereUuid($request->batch)
            ->getOrFail(trans('academic.batch.batch'), 'batch');

        $schedule = Schedule::query()
            ->whereExamId($exam->id)
            ->whereBatchId($batch->id)
            ->getOrFail(trans('exam.schedule.schedule'));

        return [
            'exam' => $exam,
            'batch' => $batch,
            'schedule' => $schedule,
        ];
    }

    public function fetch(Request $request)
    {
        $data = $this->validateInput($request);

        $exam = $data['exam'];
        $batch = $data['batch'];
        $schedule = $data['schedule'];

        $request->merge([
            'select_all' => true,
        ]);

        $students = (new FetchBatchWiseStudent)->execute($request->all());

        $notApplicableStudents = Arr::get($schedule->details, 'not_applicable_students_for_comment', []);

        $comments = collect(Arr::get($schedule->details, 'comments') ?? []);

        foreach ($students as $student) {
            $studentComment = $comments->firstWhere('uuid', $student->uuid);
            $comment = Arr::get($studentComment, 'comment');
            $result = Arr::get($studentComment, 'result');

            $student->comment = $comment;
            $student->result = $result;
            $student->is_not_applicable = in_array($student->uuid, $notApplicableStudents) ? true : false;
            $student->has_exam_mark = true;
        }

        return StudentResource::collection($students)
            ->additional([
                'meta' => [
                    'comment_recorded' => count(Arr::get($schedule->details, 'comments', [])) ? true : false,
                ],
            ]);
    }

    public function store(Request $request)
    {
        $data = $this->validateInput($request);

        $exam = $data['exam'];
        $batch = $data['batch'];
        $schedule = $data['schedule'];

        $request->merge(['select_all' => true]);

        $students = (new FetchBatchWiseStudent)->execute($request->all(), true);

        if (array_diff(Arr::pluck($request->students, 'uuid'), Arr::pluck($students, 'uuid'))) {
            throw ValidationException::withMessages(['message' => trans('general.errors.invalid_input')]);
        }

        $marks = $request->marks;
        $notApplicableStudents = $request->not_applicable_students ?? [];
        // foreach ($request->students as $input) {
        //     $student = collect($students)->where('uuid', Arr::get($input, 'uuid'))->first();
        // }

        $details = $schedule->details;
        $details['comments'] = $request->comments;
        $schedule->details = $details;
        $schedule->setConfig([
            'not_applicable_students_for_comment' => $notApplicableStudents,
        ]);
        $schedule->save();
    }

    public function remove(Request $request)
    {
        $data = $this->validateInput($request);

        $schedule = $data['schedule'];

        $details = $schedule->details;
        unset($details['comments']);
        $schedule->details = $details;

        $schedule->setConfig([
            'not_applicable_students_for_comment' => [],
        ]);
        $schedule->save();
    }

    /**
     * Apply template to selected students
     */
    public function applyTemplate(Request $request)
    {
        $data = $this->validateInput($request);

        $exam = $data['exam'];
        $batch = $data['batch'];
        $schedule = $data['schedule'];

        $template = CommentTemplate::whereUuid($request->template_uuid)->firstOrFail();
        $studentUuids = $request->student_uuids ?? [];
        $commentType = $request->comment_type ?? 'comment'; // 'comment' or 'result'

        if (empty($studentUuids)) {
            throw ValidationException::withMessages(['message' => trans('general.errors.invalid_input')]);
        }

        // Get course for template processing
        $course = $batch->course;

        // Process template content
        $processedContent = $template->processForCourse($course);

        // Replace next course name placeholder if exists
        if (strpos($processedContent, '{next_course_name}') !== false) {
            $nextCourseName = $template->getNextCourseName($course);
            $processedContent = str_replace('{next_course_name}', $nextCourseName ?? 'next level', $processedContent);
        }

        // Get existing comments
        $details = $schedule->details;
        $existingComments = collect(Arr::get($details, 'comments', []));

        // Apply template to selected students
        foreach ($studentUuids as $studentUuid) {
            $existingComment = $existingComments->firstWhere('uuid', $studentUuid);

            if ($existingComment) {
                // Update existing comment
                $existingComments = $existingComments->map(function ($comment) use ($studentUuid, $processedContent, $commentType) {
                    if ($comment['uuid'] === $studentUuid) {
                        if ($commentType === 'result') {
                            $comment['result'] = $processedContent;
                        } else {
                            $comment['comment'] = $processedContent;
                        }
                    }
                    return $comment;
                });
            } else {
                // Add new comment
                $newComment = [
                    'uuid' => $studentUuid,
                    'result' => $commentType === 'result' ? $processedContent : '',
                    'comment' => $commentType === 'comment' ? $processedContent : '',
                ];
                $existingComments->push($newComment);
            }
        }

        // Save updated comments
        $details['comments'] = $existingComments->toArray();
        $schedule->details = $details;
        $schedule->save();

        return [
            'message' => trans('exam.comment_template.applied_successfully', ['count' => count($studentUuids)]),
            'applied_count' => count($studentUuids),
        ];
    }
}
