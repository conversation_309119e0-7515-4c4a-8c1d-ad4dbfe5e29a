<?php

namespace App\Models\Exam;

use App\Concerns\HasFilter;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use App\Models\Academic\Course;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class CommentTemplate extends Model
{
    use HasFactory, HasFilter, HasMeta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $primaryKey = 'id';

    protected $table = 'comment_templates';

    protected $casts = [
        'is_active' => 'boolean',
        'course_rules' => 'array',
        'meta' => 'array',
    ];

    public function getModelName(): string
    {
        return 'CommentTemplate';
    }

    public function scopeActive(Builder $query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType(Builder $query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeAcademic(Builder $query)
    {
        return $query->where('type', 'academic');
    }

    public function scopeBehavioral(Builder $query)
    {
        return $query->where('type', 'behavioral');
    }

    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Process template content with placeholders for a given course
     */
    public function processForCourse(Course $course): string
    {
        $content = $this->content;
        
        if ($this->type === 'academic' && $this->course_rules) {
            $content = $this->processAcademicTemplate($course);
        }

        // Replace common placeholders
        $placeholders = [
            '{course_name}' => $course->name,
            '{division_name}' => $course->division->name ?? '',
            '{current_year}' => date('Y'),
        ];

        return str_replace(array_keys($placeholders), array_values($placeholders), $content);
    }

    /**
     * Process academic template with course progression rules
     */
    protected function processAcademicTemplate(Course $course): string
    {
        $rules = $this->course_rules;
        
        if (!$rules || !isset($rules['progression_rules'])) {
            return $this->content;
        }

        foreach ($rules['progression_rules'] as $rule) {
            if ($this->courseMatchesRule($course, $rule)) {
                return $rule['template'] ?? $this->content;
            }
        }

        return $this->content;
    }

    /**
     * Check if course matches a specific rule
     */
    protected function courseMatchesRule(Course $course, array $rule): bool
    {
        if (isset($rule['course_name_pattern'])) {
            return preg_match('/' . $rule['course_name_pattern'] . '/i', $course->name);
        }

        if (isset($rule['course_uuid'])) {
            return $course->uuid === $rule['course_uuid'];
        }

        return false;
    }

    /**
     * Get next course name for progression
     */
    public function getNextCourseName(Course $currentCourse): ?string
    {
        // Extract year/level from course name (e.g., "Year 1" -> "Year 2")
        if (preg_match('/year\s+(\d+)/i', $currentCourse->name, $matches)) {
            $currentYear = (int) $matches[1];
            $nextYear = $currentYear + 1;
            return preg_replace('/year\s+\d+/i', "Year {$nextYear}", $currentCourse->name);
        }

        // Extract grade/class from course name (e.g., "Grade 5" -> "Grade 6")
        if (preg_match('/grade\s+(\d+)/i', $currentCourse->name, $matches)) {
            $currentGrade = (int) $matches[1];
            $nextGrade = $currentGrade + 1;
            return preg_replace('/grade\s+\d+/i', "Grade {$nextGrade}", $currentCourse->name);
        }

        // Extract class from course name (e.g., "Class 3" -> "Class 4")
        if (preg_match('/class\s+(\d+)/i', $currentCourse->name, $matches)) {
            $currentClass = (int) $matches[1];
            $nextClass = $currentClass + 1;
            return preg_replace('/class\s+\d+/i', "Class {$nextClass}", $currentCourse->name);
        }

        return null;
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('comment_template')
            ->logAll()
            ->logExcept(['updated_at'])
            ->logOnlyDirty();
    }
}
