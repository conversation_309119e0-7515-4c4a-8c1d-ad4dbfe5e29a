import { computed, watch, reactive } from 'vue'
import { useRoute } from 'vue-router'

/**
 * Composable for managing marksheet filter state and validation
 * Provides reactive form state, validation logic, and persistence
 */
export function useMarksheetFilters(pageType = 'legacy') {
    const route = useRoute()
    
    // Storage key for persistence
    const storageKey = `marksheet_filters_${pageType}`
    
    // Initialize form state
    const form = reactive({
        type: '',
        term: '',
        exam: '',
        batch: '',
        students: [],
        attempt: '',
        cumulativeAssessment: false,
        resultDate: '',
        column: 1,
        marginTop: 0,
        showCourseWise: false,
        showAllStudent: false,
        showSummaryReport: false,
        sortSummaryReportByRank: false,
    })
    
    // Load persisted state
    const loadPersistedState = () => {
        try {
            const saved = sessionStorage.getItem(storageKey)
            if (saved) {
                const parsedState = JSON.parse(saved)
                Object.assign(form, parsedState)
            }
        } catch (error) {
            console.warn('Failed to load persisted marksheet filters:', error)
        }
    }
    
    // Save state to sessionStorage
    const persistState = () => {
        try {
            sessionStorage.setItem(storageKey, JSON.stringify(form))
        } catch (error) {
            console.warn('Failed to persist marksheet filters:', error)
        }
    }
    
    // Watch form changes and persist
    watch(form, persistState, { deep: true })
    
    // Get validation rules based on exam type
    const getValidationRules = (examType) => {
        const baseRules = ['type', 'batch']
        
        switch (examType) {
            case 'exam_wise':
            case 'exam_wise_default':
            case 'exam_wise_credit_based':
            case 'exam_wise_cameroon':
                return [...baseRules, 'exam', 'students']
                
            case 'term_wise':
            case 'term_wise_cameroon':
                return [...baseRules, 'term', 'students']
                
            case 'cumulative':
                return [...baseRules, 'students']
                
            default:
                return [...baseRules, 'students']
        }
    }
    
    // Get additional rules for print page
    const getPrintPageRules = (examType) => {
        const rules = getValidationRules(examType)
        return [...rules, 'attempt']
    }
    
    // Check if a field has a valid value
    const isFieldValid = (fieldName, value) => {
        if (fieldName === 'students') {
            return Array.isArray(value) && value.length > 0
        }
        return value && value !== ''
    }
    
    // Validate form based on current type and page
    const validateForm = computed(() => {
        if (!form.type) return false
        
        const rules = pageType === 'print' 
            ? getPrintPageRules(form.type)
            : getValidationRules(form.type)
        
        return rules.every(fieldName => {
            const value = form[fieldName]
            return isFieldValid(fieldName, value)
        })
    })
    
    // Check if specific exam/term is required
    const requiresExam = computed(() => {
        return ['exam_wise', 'exam_wise_default', 'exam_wise_credit_based', 'exam_wise_cameroon'].includes(form.type)
    })
    
    const requiresTerm = computed(() => {
        return ['term_wise', 'term_wise_cameroon'].includes(form.type)
    })
    
    const requiresAttempt = computed(() => {
        return pageType === 'print'
    })
    
    // Get missing fields for user feedback
    const getMissingFields = computed(() => {
        if (!form.type) return ['type']
        
        const rules = pageType === 'print' 
            ? getPrintPageRules(form.type)
            : getValidationRules(form.type)
        
        return rules.filter(fieldName => {
            const value = form[fieldName]
            return !isFieldValid(fieldName, value)
        })
    })
    
    // Initialize from route query if available
    const initializeFromRoute = () => {
        if (route.query.type) form.type = route.query.type
        if (route.query.term) form.term = route.query.term
        if (route.query.exam) form.exam = route.query.exam
        if (route.query.batch) form.batch = route.query.batch
        if (route.query.attempt) form.attempt = route.query.attempt
        if (route.query.students) {
            form.students = route.query.students.split(',')
        }
        
        // Boolean fields
        form.showCourseWise = route.query.showCourseWise === 'true'
        form.showAllStudent = route.query.showAllStudent === 'true'
        form.showSummaryReport = route.query.showSummaryReport === 'true'
        form.sortSummaryReportByRank = route.query.sortSummaryReportByRank === 'true'
        form.cumulativeAssessment = route.query.cumulativeAssessment === 'true'
    }
    
    // Clear all filters
    const clearFilters = () => {
        Object.keys(form).forEach(key => {
            if (Array.isArray(form[key])) {
                form[key] = []
            } else if (typeof form[key] === 'boolean') {
                form[key] = false
            } else if (typeof form[key] === 'number') {
                form[key] = key === 'column' ? 1 : 0
            } else {
                form[key] = ''
            }
        })
        
        // Clear persisted state
        try {
            sessionStorage.removeItem(storageKey)
        } catch (error) {
            console.warn('Failed to clear persisted state:', error)
        }
    }
    
    // Get form data for API calls
    const getFormData = () => {
        return { ...form }
    }
    
    // Get query parameters for URLs
    const getQueryParams = () => {
        const params = {}

        Object.entries(form).forEach(([key, value]) => {
            if (value !== '' && value !== null && value !== undefined) {
                // Skip irrelevant parameters based on exam type
                if (form.type === 'cumulative' && (key === 'term' || key === 'exam')) {
                    return // Don't include term/exam for cumulative type
                }
                if (form.type === 'term_wise' && key === 'exam') {
                    return // Don't include exam for term_wise type
                }
                if (['exam_wise', 'exam_wise_default', 'exam_wise_credit_based', 'exam_wise_cameroon'].includes(form.type) && key === 'term') {
                    return // Don't include term for exam_wise types
                }

                // Convert camelCase to snake_case for backend compatibility
                const backendKey = key === 'marginTop' ? 'margin_top' :
                                  key === 'resultDate' ? 'result_date' :
                                  key === 'showCourseWise' ? 'show_course_wise' :
                                  key === 'showAllStudent' ? 'show_all_student' :
                                  key === 'showSummaryReport' ? 'show_summary_report' :
                                  key === 'sortSummaryReportByRank' ? 'sort_summary_report_by_rank' :
                                  key === 'cumulativeAssessment' ? 'cumulative_assessment' : key

                if (Array.isArray(value) && value.length > 0) {
                    params[backendKey] = value.join(',')
                } else if (typeof value === 'boolean') {
                    params[backendKey] = value.toString()
                } else {
                    params[backendKey] = value
                }
            }
        })

        return params
    }
    
    // Initialize on creation
    loadPersistedState()
    initializeFromRoute()
    
    return {
        form,
        validateForm,
        requiresExam,
        requiresTerm,
        requiresAttempt,
        getMissingFields,
        clearFilters,
        getFormData,
        getQueryParams,
        persistState,
        loadPersistedState,
        initializeFromRoute
    }
}
