<?php

namespace Tests\Feature\Exam;

use App\Models\Academic\Batch;
use App\Models\Academic\Course;
use App\Models\Academic\Subject;
use App\Models\Exam\Exam;
use App\Models\Exam\Schedule;
use App\Models\Exam\Term;
use App\Models\Student\Student;
use App\Services\Exam\CumulativeMarksheetService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CumulativeMarksheetFilteringTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    /** @test */
    public function it_excludes_subjects_without_exams_from_cumulative_marksheet()
    {
        // Create test data
        $batch = Batch::factory()->create();
        $course = $batch->course;
        
        // Create subjects - one with exam, one without
        $subjectWithExam = Subject::factory()->create(['name' => 'Mathematics']);
        $subjectWithoutExam = Subject::factory()->create(['name' => 'Art']);
        
        // Create term and exam
        $term = Term::factory()->create();
        $exam = Exam::factory()->create(['term_id' => $term->id]);
        
        // Create schedule with only one subject having exam
        $schedule = Schedule::factory()->create([
            'exam_id' => $exam->id,
            'batch_id' => $batch->id,
        ]);
        
        // Create exam record only for Mathematics (has_exam = true)
        $schedule->records()->create([
            'subject_id' => $subjectWithExam->id,
            'config' => json_encode([
                'has_exam' => true,
                'assessments' => [
                    ['code' => 'test', 'name' => 'Test', 'max_mark' => 100]
                ]
            ]),
            'marks' => []
        ]);
        
        // Create exam record for Art but with has_exam = false
        $schedule->records()->create([
            'subject_id' => $subjectWithoutExam->id,
            'config' => json_encode([
                'has_exam' => false,
                'assessments' => []
            ]),
            'marks' => []
        ]);
        
        // Create a student
        $student = Student::factory()->create(['batch_id' => $batch->id]);
        
        // Test the service
        $service = new CumulativeMarksheetService();
        
        $params = [
            'batch' => $batch->uuid,
            'term' => [$term->uuid],
            'students' => [$student->uuid],
        ];
        
        $result = $service->fetch($params);
        
        // Verify that only Mathematics appears in the result
        $this->assertNotEmpty($result['students']);
        
        $studentData = $result['students'][0];
        $this->assertNotEmpty($studentData->rows);
        
        // Check that Mathematics is included
        $subjectNames = collect($studentData->rows)->pluck('0.label')->filter();
        $this->assertTrue($subjectNames->contains('Mathematics'));
        
        // Check that Art is NOT included
        $this->assertFalse($subjectNames->contains('Art'));
    }

    /** @test */
    public function it_excludes_not_applicable_students_from_totals()
    {
        // Create test data
        $batch = Batch::factory()->create();
        $subject = Subject::factory()->create(['name' => 'Mathematics']);
        $term = Term::factory()->create();
        $exam = Exam::factory()->create(['term_id' => $term->id]);
        
        // Create students
        $student1 = Student::factory()->create(['batch_id' => $batch->id]);
        $student2 = Student::factory()->create(['batch_id' => $batch->id]);
        
        // Create schedule
        $schedule = Schedule::factory()->create([
            'exam_id' => $exam->id,
            'batch_id' => $batch->id,
        ]);
        
        // Create exam record with student2 as not applicable
        $schedule->records()->create([
            'subject_id' => $subject->id,
            'config' => json_encode([
                'has_exam' => true,
                'not_applicable_students' => [$student2->uuid],
                'assessments' => [
                    ['code' => 'test', 'name' => 'Test', 'max_mark' => 100]
                ]
            ]),
            'marks' => [
                [
                    'code' => 'test',
                    'marks' => [
                        ['uuid' => $student1->uuid, 'obtained_mark' => 80],
                        ['uuid' => $student2->uuid, 'obtained_mark' => 90], // Should be ignored
                    ]
                ]
            ]
        ]);
        
        // Test the service
        $service = new CumulativeMarksheetService();
        
        $params = [
            'batch' => $batch->uuid,
            'term' => [$term->uuid],
            'students' => [$student1->uuid, $student2->uuid],
        ];
        
        $result = $service->fetch($params);
        
        // Verify results
        $this->assertCount(2, $result['students']);
        
        // Student 1 should have normal marks
        $student1Data = collect($result['students'])->firstWhere('uuid', $student1->uuid);
        $this->assertNotNull($student1Data);
        
        // Student 2 should have 'NA' for the subject
        $student2Data = collect($result['students'])->firstWhere('uuid', $student2->uuid);
        $this->assertNotNull($student2Data);
        
        // Check that student2 has 'NA' in their subject row
        $student2SubjectRow = collect($student2Data->rows)->first();
        $this->assertEquals('NA', $student2SubjectRow[2]['label']); // Assuming exam column is at index 2
    }
}
