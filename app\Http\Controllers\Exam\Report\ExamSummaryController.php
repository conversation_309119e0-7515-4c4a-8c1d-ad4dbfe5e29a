<?php

namespace App\Http\Controllers\Exam\Report;

use App\Http\Controllers\Controller;
use App\Services\Exam\Report\ExamSummaryService;
use Illuminate\Http\Request;

class ExamSummaryController extends Controller
{
    public function preRequisite(Request $request, ExamSummaryService $service)
    {
        return response()->ok($service->preRequisite($request));
    }

    public function fetchReport(Request $request, ExamSummaryService $service)
    {
        return $service->fetchReport($request);
    }

    public function downloadPdf(Request $request, ExamSummaryService $service)
    {
        // Set action to pdf to trigger PDF generation
        $request->merge(['action' => 'pdf']);

        // Make sure paper_size is properly passed
        if ($request->has('paper_size')) {
            $request->merge(['paper_size' => $request->paper_size]);
        }

        return $service->fetchReport($request);
    }
}
