{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "chillerlan/php-qrcode": "^5.0", "danharrin/livewire-rate-limiting": "^1.2", "guzzlehttp/guzzle": "^7.5", "intervention/image": "^2.7", "laravel/folio": "^1.1", "laravel/framework": "^11.0", "laravel/helpers": "^1.6", "laravel/horizon": "^5.15.0", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.6.1", "laravel/tinker": "^2.8.1", "laravel/ui": "^4.2.1", "league/commonmark": "^2.3.9", "league/flysystem-aws-s3-v3": "^3.0", "livewire/livewire": "^3.1", "maatwebsite/excel": "^3.1.48", "mews/purifier": "^3.4", "monolog/monolog": "^3.0", "mpdf/mpdf": "^8.1.4", "opcodesio/log-viewer": "^3.6.0", "predis/predis": "^2.1.2", "pusher/pusher-php-server": "^7.2.2", "razorpay/razorpay": "^2.8", "socialiteproviders/microsoft": "^4.2", "spatie/browsershot": "^5.0", "spatie/laravel-activitylog": "^4.7.3", "spatie/laravel-backup": "^8.1.7", "spatie/laravel-permission": "^6.0", "spatie/valuestore": "^1.3.2", "stripe/stripe-php": "^12.4", "symfony/http-client": "^6.2.7", "symfony/mailgun-mailer": "^6.2.7", "twilio/sdk": "^8.2", "ua-parser/uap-php": "^3.9.14", "web-token/jwt-core": "2.2.10", "web-token/jwt-key-mgmt": "2.2.10", "web-token/jwt-signature": "2.2.10", "web-token/jwt-signature-algorithm-hmac": "2.2.10"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8.1", "fakerphp/faker": "^1.21", "laravel/pint": "^1.6", "mockery/mockery": "^1.5.1", "nunomaduro/collision": "^8.1", "pestphp/pest": "^2.6", "pestphp/pest-plugin-faker": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "spatie/pest-plugin-test-time": "^2.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Io\\Billdesk\\Client\\": "billdesk/"}, "files": ["app/Helpers/helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true, "pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}